<?php
if (!defined('ABSPATH')) {
    exit;
}

// This file contains just the tab content for the PWA settings
?>

<!-- General Tab -->
<div class="q-pwa-tab-content active" id="tab-general">
    <div class="q-pwa-tab-header">
        <h2>General Settings</h2>
        <p>Configure basic Progressive Web App settings for your site.</p>
    </div>

    <table class="form-table" role="presentation">
        <tbody>
            <tr>
                <th scope="row">Enable PWA</th>
                <td>
                    <?php Q_PWA_Settings::render_checkbox_field(['field' => 'q_pwa_enabled', 'description' => 'Enable Progressive Web App functionality']); ?>
                </td>
            </tr>
            <tr>
                <th scope="row">App Name</th>
                <td>
                    <?php Q_PWA_Settings::render_text_field(['field' => 'q_pwa_app_name', 'description' => 'Full name of your app (max 45 characters)']); ?>
                </td>
            </tr>
            <tr>
                <th scope="row">Short Name</th>
                <td>
                    <?php Q_PWA_Settings::render_text_field(['field' => 'q_pwa_app_short_name', 'description' => 'Short name for home screen (max 12 characters)']); ?>
                </td>
            </tr>
            <tr>
                <th scope="row">Description</th>
                <td>
                    <?php Q_PWA_Settings::render_textarea_field(['field' => 'q_pwa_app_description', 'description' => 'Brief description of your app']); ?>
                </td>
            </tr>
            <tr>
                <th scope="row">Start URL</th>
                <td>
                    <?php Q_PWA_Settings::render_text_field(['field' => 'q_pwa_start_url', 'description' => 'URL to load when app is launched (relative to site root)']); ?>
                </td>
            </tr>
            <tr>
                <th scope="row">Offline Fallback Page</th>
                <td>
                    <?php Q_PWA_Settings::render_page_dropdown_field(['field' => 'q_pwa_offline_page_id', 'description' => 'Select the WordPress page to show when offline.']); ?>
                </td>
            </tr>
            <tr>
                <th scope="row">Caching Strategy</th>
                <td>
                    <?php Q_PWA_Settings::render_select_field([
                        'field' => 'q_pwa_caching_strategy',
                        'options' => [
                            'cache-first' => 'Cache First',
                            'network-first' => 'Network First',
                            'stale-while-revalidate' => 'Stale While Revalidate'
                        ],
                        'description' => 'Choose how the service worker should cache resources.'
                    ]); ?>
                </td>
            </tr>
            <tr>
                <th scope="row">App Scope</th>
                <td>
                    <?php Q_PWA_Settings::render_text_field(['field' => 'q_pwa_scope', 'description' => 'Navigation scope for the PWA (default: /)']); ?>
                </td>
            </tr>
            <tr>
                <th scope="row">App Categories</th>
                <td>
                    <?php Q_PWA_Settings::render_text_field(['field' => 'q_pwa_categories', 'description' => 'Comma-separated list of app categories (e.g., productivity, utilities, social)']); ?>
                </td>
            </tr>
        </tbody>
    </table>
</div>

<!-- iOS Settings Tab -->
<div class="q-pwa-tab-content" id="tab-ios">
    <div class="q-pwa-tab-header">
        <h2>iOS-specific Settings</h2>
        <p>Configure settings specific to iOS devices for optimal PWA experience.</p>
    </div>

    <table class="form-table" role="presentation">
        <tbody>
            <tr>
                <th scope="row">Status Bar Style</th>
                <td>
                    <?php Q_PWA_Settings::render_select_field([
                        'field' => 'q_pwa_ios_status_bar_style',
                        'options' => [
                            'default' => 'Default',
                            'black' => 'Black',
                            'black-translucent' => 'Black Translucent'
                        ],
                        'description' => 'iOS status bar appearance'
                    ]); ?>
                </td>
            </tr>
            <tr>
                <th scope="row">Web App Capable</th>
                <td>
                    <?php Q_PWA_Settings::render_checkbox_field(['field' => 'q_pwa_ios_web_app_capable', 'description' => 'Run as a standalone web application on iOS']); ?>
                </td>
            </tr>
        </tbody>
    </table>
</div>

<!-- Android Settings Tab -->
<div class="q-pwa-tab-content" id="tab-android">
    <div class="q-pwa-tab-header">
        <h2>Android-specific Settings</h2>
        <p>Configure settings specific to Android devices for optimal PWA experience.</p>
    </div>

    <table class="form-table" role="presentation">
        <tbody>
            <tr>
                <th scope="row">Theme Color</th>
                <td>
                    <?php Q_PWA_Settings::render_color_field(['field' => 'q_pwa_theme_color', 'description' => 'Color for Android browser UI and status bar']); ?>
                </td>
            </tr>
            <tr>
                <th scope="row">Navigation Bar Color</th>
                <td>
                    <?php Q_PWA_Settings::render_color_field(['field' => 'q_pwa_nav_color', 'description' => 'Color for Android navigation bar']); ?>
                </td>
            </tr>
            <tr>
                <th scope="row">Monochrome Icon</th>
                <td>
                    <?php Q_PWA_Settings::render_checkbox_field(['field' => 'q_pwa_monochrome_icon', 'description' => 'Support monochrome icon for Android']); ?>
                </td>
            </tr>
        </tbody>
    </table>
</div>

<!-- Desktop Settings Tab -->
<div class="q-pwa-tab-content" id="tab-desktop">
    <div class="q-pwa-tab-header">
        <h2>Desktop-specific Settings</h2>
        <p>Configure settings specific to desktop browsers for optimal PWA experience.</p>
    </div>

    <table class="form-table" role="presentation">
        <tbody>
            <tr>
                <th scope="row">Window Controls Overlay</th>
                <td>
                    <?php Q_PWA_Settings::render_checkbox_field(['field' => 'q_pwa_window_controls_overlay', 'description' => 'Enable window controls overlay for desktop PWA']); ?>
                </td>
            </tr>
            <tr>
                <th scope="row">Display Mode</th>
                <td>
                    <?php Q_PWA_Settings::render_select_field([
                        'field' => 'q_pwa_display_mode',
                        'options' => [
                            'standalone' => 'Standalone (Recommended)',
                            'fullscreen' => 'Fullscreen',
                            'minimal-ui' => 'Minimal UI',
                            'browser' => 'Browser'
                        ],
                        'description' => 'How the app should be displayed when launched'
                    ]); ?>
                </td>
            </tr>
            <tr>
                <th scope="row">Orientation</th>
                <td>
                    <?php Q_PWA_Settings::render_select_field([
                        'field' => 'q_pwa_orientation',
                        'options' => [
                            'any' => 'Any',
                            'portrait' => 'Portrait',
                            'landscape' => 'Landscape'
                        ],
                        'description' => 'Preferred screen orientation'
                    ]); ?>
                </td>
            </tr>
            <tr>
                <th scope="row">Allow Install on Desktop</th>
                <td>
                    <?php Q_PWA_Settings::render_radio_field([
                        'field' => 'q_pwa_installable_on_desktop',
                        'options' => [1 => 'Yes', 0 => 'No'],
                        'description' => 'Allow users to install the PWA on desktop devices.'
                    ]); ?>
                </td>
            </tr>
        </tbody>
    </table>

    <h3>Protocol Handlers</h3>
    <table class="form-table" role="presentation">
        <tbody>
            <tr>
                <th scope="row">Enable Protocol Handlers</th>
                <td>
                    <?php Q_PWA_Settings::render_checkbox_field(['field' => 'q_pwa_protocol_handlers', 'description' => 'Enable protocol handlers for desktop PWA']); ?>
                </td>
            </tr>
        </tbody>
    </table>
</div>

<!-- Advanced Features Tab -->
<div class="q-pwa-tab-content" id="tab-advanced">
    <div class="q-pwa-tab-header">
        <h2>Advanced PWA Features</h2>
        <p>Configure advanced Progressive Web App features for enhanced functionality.</p>
    </div>

    <h3>App Shortcuts</h3>
    <table class="form-table" role="presentation">
        <tbody>
            <tr>
                <th scope="row">Enable Shortcuts</th>
                <td>
                    <?php Q_PWA_Settings::render_checkbox_field(['field' => 'q_pwa_shortcuts_enabled', 'description' => 'Enable app shortcuts (up to 4 shortcuts)']); ?>
                </td>
            </tr>
        </tbody>
    </table>

    <div id="q-pwa-shortcuts-config" style="display: none;">
        <h4>Configure Shortcuts</h4>
        <?php for ($i = 1; $i <= 4; $i++): ?>
            <div class="q-pwa-shortcut-group">
                <h5>Shortcut <?php echo $i; ?></h5>
                <table class="form-table" role="presentation">
                    <tbody>
                        <tr>
                            <th scope="row">Name</th>
                            <td>
                                <?php Q_PWA_Settings::render_text_field(['field' => "q_pwa_shortcut_{$i}_name", 'description' => 'Display name for the shortcut']); ?>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">URL</th>
                            <td>
                                <?php Q_PWA_Settings::render_text_field(['field' => "q_pwa_shortcut_{$i}_url", 'description' => 'URL to open when shortcut is activated']); ?>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Description</th>
                            <td>
                                <?php Q_PWA_Settings::render_text_field(['field' => "q_pwa_shortcut_{$i}_description", 'description' => 'Optional description for the shortcut']); ?>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Icon</th>
                            <td>
                                <?php Q_PWA_Settings::render_media_field(['field' => "q_pwa_shortcut_{$i}_icon", 'description' => 'Icon for the shortcut (96x96 pixels recommended)']); ?>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        <?php endfor; ?>
    </div>

    <div class="notice notice-info inline">
        <p><strong>Note:</strong> Advanced features may not be supported by all browsers and platforms. Test thoroughly before deploying to production.</p>
    </div>
</div>

<!-- Images & Splash Screens Tab -->
<div class="q-pwa-tab-content" id="tab-images">
    <div class="q-pwa-tab-header">
        <h2>Images & Splash Screens</h2>
        <p>Upload a single app icon and splash screen image. We'll automatically generate all required sizes for your PWA.</p>
    </div>

    <table class="form-table" role="presentation">
        <tbody>
            <tr>
                <th scope="row">App Icon</th>
                <td>
                    <?php Q_PWA_Settings::render_media_field(['field' => 'q_pwa_icon', 'description' => 'Upload a high-quality square image (at least 512x512 pixels). This will be used for all app icons.']); ?>
                    <p class="description">This single icon will be used to generate all required sizes for Android, iOS, and other platforms.</p>
                </td>
            </tr>
            <tr>
                <th scope="row">Splash Screen</th>
                <td>
                    <?php Q_PWA_Settings::render_media_field(['field' => 'q_pwa_splash', 'description' => 'Upload a high-quality image for splash screens (at least 2048x2732 pixels).']); ?>
                    <p class="description">This image will be used for splash screens on all devices.</p>
                </td>
            </tr>
            <tr>
                <th scope="row">Background Color</th>
                <td>
                    <?php Q_PWA_Settings::render_color_field(['field' => 'q_pwa_background_color', 'description' => 'Background color for splash screen and app icon padding']); ?>
                </td>
            </tr>
        </tbody>
    </table>
</div>

<!-- Analytics Tab -->
<div class="q-pwa-tab-content" id="tab-analytics">
    <div class="q-pwa-tab-header">
        <h2>PWA Analytics</h2>
        <p>Track and analyze how users interact with your Progressive Web App.</p>
    </div>

    <table class="form-table" role="presentation">
        <tbody>
            <tr>
                <th scope="row">Enable Analytics</th>
                <td>
                    <?php Q_PWA_Settings::render_checkbox_field(['field' => 'q_pwa_analytics_enabled', 'description' => 'Collect usage data for your PWA']); ?>
                </td>
            </tr>
            <tr>
                <th scope="row">Data Retention</th>
                <td>
                    <?php Q_PWA_Settings::render_select_field([
                        'field' => 'q_pwa_analytics_retention',
                        'options' => [
                            '30' => '30 Days',
                            '60' => '60 Days',
                            '90' => '90 Days',
                            '180' => '180 Days',
                            '365' => '1 Year'
                        ],
                        'description' => 'How long to keep analytics data before automatic deletion'
                    ]); ?>
                </td>
            </tr>
        </tbody>
    </table>

    <?php
    // Include the analytics dashboard template
    if (file_exists(Q_PLUGIN_DIR . 'includes/templates/pwa-analytics-dashboard.php')) {
        include_once Q_PLUGIN_DIR . 'includes/templates/pwa-analytics-dashboard.php';
    } else {
        // Fallback to basic analytics display
        $analytics_data = function_exists('q_get_pwa_analytics') ? q_get_pwa_analytics() : [];
        if (!empty($analytics_data)) :
    ?>
            <h3>Basic Analytics Data</h3>
            <table class="wp-list-table widefat striped">
                <thead>
                    <tr>
                        <th>Event Type</th>
                        <th>Count</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($analytics_data as $data) : ?>
                        <tr>
                            <td><?php echo esc_html($data['event_name']); ?></td>
                            <td><?php echo esc_html($data['count']); ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php else : ?>
            <p>No analytics data is available yet.</p>
    <?php endif;
    }
    ?>
</div>

<!-- Shortcodes Tab -->
<div class="q-pwa-tab-content" id="tab-shortcodes">
    <div class="q-pwa-tab-header">
        <h2>Available Shortcodes Reference</h2>
        <p>Use these shortcodes to display PWA analytics and features in your pages, posts, or widgets. Copy and paste the examples as needed.</p>
    </div>

    <!-- PWA Feature Shortcodes -->
    <div class="q-pwa-shortcode-section">
        <h3> PWA Feature Shortcodes</h3>
        <table class="wp-list-table widefat fixed striped q-pwa-shortcode-table">
            <thead>
                <tr>
                    <th style="width: 25%;">Shortcode</th>
                    <th style="width: 35%;">Description & Parameters</th>
                    <th style="width: 40%;">Example Usage</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><code>[q_subscribe_button]</code></td>
                    <td>
                        <strong>Displays a push notification subscribe button</strong><br>
                        <small>
                            <strong>text</strong> (default: "Enable Notifications"): Button text<br>
                            <strong>class</strong> (default: "ui-button primary"): CSS classes<br>
                            <strong>icon</strong> (default: "bi bi-bell"): Icon class
                        </small>
                    </td>
                    <td>
                        <code>[q_subscribe_button]</code><br>
                        <code>[q_subscribe_button text="Join Alerts" class="custom-btn"]</code><br>
                        <code>[q_subscribe_button icon="bi bi-bell-plus"]</code>
                    </td>
                </tr>
                <tr>
                    <td><code>[q_unsubscribe_button]</code></td>
                    <td>
                        <strong>Displays a push notification unsubscribe button</strong><br>
                        <small>
                            <strong>text</strong> (default: "Disable Notifications"): Button text<br>
                            <strong>class</strong> (default: "ui-button"): CSS classes<br>
                            <strong>icon</strong> (default: "bi bi-bell-slash"): Icon class
                        </small>
                    </td>
                    <td>
                        <code>[q_unsubscribe_button]</code><br>
                        <code>[q_unsubscribe_button text="Turn Off Alerts"]</code><br>
                        <code>[q_unsubscribe_button class="btn-danger"]</code>
                    </td>
                </tr>
                <tr>
                    <td><code>[q_notification_inbox]</code></td>
                    <td>
                        <strong>Shows notification inbox for logged-in user</strong><br>
                        <small>
                            <strong>limit</strong> (default: 20): Number of notifications to display
                        </small>
                    </td>
                    <td>
                        <code>[q_notification_inbox]</code><br>
                        <code>[q_notification_inbox limit="10"]</code><br>
                        <code>[q_notification_inbox limit="50"]</code>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    <!-- Holistic Analytics Shortcodes -->
    <div class="q-pwa-shortcode-section">
        <h3>Holistic Analytics Shortcodes</h3>
        <table class="wp-list-table widefat fixed striped q-pwa-shortcode-table">
            <thead>
                <tr>
                    <th style="width: 25%;">Shortcode</th>
                    <th style="width: 35%;">Description & Parameters</th>
                    <th style="width: 40%;">Example Usage</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><code>[pwa_analytics]</code></td>
                    <td>
                        <strong>Displays PWA analytics event counts</strong><br>
                        <small>
                            <strong>event_type</strong> (required): install, push_subscribed, appstart, pageview, session_end, etc.<br>
                            <strong>period</strong> (default: all): all, today, yesterday, week, month<br>
                            <strong>format</strong> (default: number): number, percentage, chart
                        </small>
                    </td>
                    <td>
                        <code>[pwa_analytics event_type="install" period="week"]</code><br>
                        <code>[pwa_analytics event_type="push_subscribed" format="percentage"]</code><br>
                        <code>[pwa_analytics event_type="appstart" period="today"]</code>
                    </td>
                </tr>
                <tr>
                    <td><code>[pwa_analytics]</code><br><small>Downloads</small></td>
                    <td>Number of PWA installations/downloads</td>
                    <td><code>[pwa_analytics event_type="install"]</code></td>
                </tr>
                <tr>
                    <td><code>[pwa_analytics]</code><br><small>Subscribers</small></td>
                    <td>Number of push notification subscribers</td>
                    <td><code>[pwa_analytics event_type="push_subscribed"]</code></td>
                </tr>
                <tr>
                    <td><code>[pwa_analytics]</code><br><small>App Launches</small></td>
                    <td>Number of PWA app launches</td>
                    <td><code>[pwa_analytics event_type="appstart"]</code></td>
                </tr>
                <tr>
                    <td><code>[pwa_analytics]</code><br><small>Page Views</small></td>
                    <td>Number of page views in PWA mode</td>
                    <td><code>[pwa_analytics event_type="pageview" period="week"]</code></td>
                </tr>
                <tr>
                    <td><code>[pwa_analytics]</code><br><small>Sessions</small></td>
                    <td>Number of completed user sessions</td>
                    <td><code>[pwa_analytics event_type="session_end" period="month"]</code></td>
                </tr>
            </tbody>
        </table>
    </div>

    <!-- User-Specific Analytics Shortcodes -->
    <div class="q-pwa-shortcode-section">
        <h3>User-Specific Analytics Shortcodes</h3>
        <table class="wp-list-table widefat fixed striped q-pwa-shortcode-table">
            <thead>
                <tr>
                    <th style="width: 25%;">Shortcode</th>
                    <th style="width: 35%;">Description & Parameters</th>
                    <th style="width: 40%;">Example Usage</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><code>[pwa_user_analytics]</code></td>
                    <td>
                        <strong>Shows user-specific analytics data</strong><br>
                        <small>
                            <strong>user_id</strong> (optional): Defaults to current logged-in user<br>
                            <strong>event</strong> (required): connection_online, devices, last_online, avg_session_duration, most_visited, push_enabled, device_list, os, browser, app_version<br>
                            <strong>format</strong> (optional, for last_online): relative, date, time, datetime
                        </small>
                    </td>
                    <td>
                        <code>[pwa_user_analytics event="connection_online"]</code><br>
                        <code>[pwa_user_analytics user_id="123" event="devices"]</code>
                    </td>
                </tr>
                <tr>
                    <td><code>[pwa_user_analytics]</code><br><small>Online Status</small></td>
                    <td>Shows if current user is online/offline</td>
                    <td><code>[pwa_user_analytics event="connection_online"]</code></td>
                </tr>
                <tr>
                    <td><code>[pwa_user_analytics]</code><br><small>Device Count</small></td>
                    <td>Number of devices user has used</td>
                    <td><code>[pwa_user_analytics event="devices"]</code></td>
                </tr>
                <tr>
                    <td><code>[pwa_user_analytics]</code><br><small>Last Online</small></td>
                    <td>
                        When user was last online<br>
                        <small>
                            <strong>format options:</strong><br>
                            • relative (default): "2 hours ago"<br>
                            • date: "July 2, 2025"<br>
                            • time: "9:58 AM"<br>
                            • datetime: "July 2, 2025 at 9:58 AM"
                        </small>
                    </td>
                    <td>
                        <code>[pwa_user_analytics event="last_online"]</code><br>
                        <code>[pwa_user_analytics event="last_online" format="date"]</code><br>
                        <code>[pwa_user_analytics event="last_online" format="time"]</code>
                    </td>
                </tr>
                <tr>
                    <td><code>[pwa_user_analytics]</code><br><small>Session Duration</small></td>
                    <td>Average session duration for user<br><small>Excludes sessions shorter than 10 seconds<br>Format: "5 minutes 30 seconds", "1 hour 20 minutes"</small></td>
                    <td><code>[pwa_user_analytics event="avg_session_duration"]</code></td>
                </tr>
                <tr>
                    <td><code>[pwa_user_analytics]</code><br><small>Most Visited</small></td>
                    <td>User's most visited page</td>
                    <td><code>[pwa_user_analytics event="most_visited"]</code></td>
                </tr>
                <tr>
                    <td><code>[pwa_user_analytics]</code><br><small>Push Status</small></td>
                    <td>Whether user has push notifications enabled</td>
                    <td><code>[pwa_user_analytics event="push_enabled"]</code></td>
                </tr>
                <tr>
                    <td><code>[pwa_user_analytics]</code><br><small>Device List</small></td>
                    <td>List of user's subscribed devices</td>
                    <td><code>[pwa_user_analytics event="device_list"]</code></td>
                </tr>
                <tr>
                    <td><code>[pwa_user_analytics]</code><br><small>Operating System</small></td>
                    <td>User's operating system</td>
                    <td><code>[pwa_user_analytics event="os"]</code></td>
                </tr>
                <tr>
                    <td><code>[pwa_user_analytics]</code><br><small>Browser</small></td>
                    <td>User's browser information</td>
                    <td><code>[pwa_user_analytics event="browser"]</code></td>
                </tr>
                <tr>
                    <td><code>[pwa_user_analytics]</code><br><small>App Version</small></td>
                    <td>User's PWA app version</td>
                    <td><code>[pwa_user_analytics event="app_version"]</code></td>
                </tr>
            </tbody>
        </table>
    </div>

    <!-- Usage Notes -->
    <div class="q-pwa-shortcode-notes">
        <h4>Usage Notes</h4>
        <ul>
            <li><strong>User Authentication:</strong> User-specific shortcodes require users to be logged in. If no user_id is specified, the current logged-in user's ID is used automatically.</li>
            <li><strong>Default Behavior:</strong> The [pwa_user_analytics] shortcode now defaults to using the current user's ID when user_id parameter is not specified.</li>
            <li><strong>Period Filtering:</strong> Use period parameter to filter analytics data: "today", "yesterday", "week", "month", or "all".</li>
            <li><strong>Format Options:</strong> Analytics can be displayed as "number" (default), "percentage", or "chart" format.</li>
            <li><strong>Placement:</strong> All shortcodes can be used in pages, posts, widgets, or theme templates.</li>
        </ul>
    </div>
</div>

<!-- Subscribers -->
<div class="q-pwa-tab-content" id="tab-subscribers">
    <?php
    if (!defined('ABSPATH')) {
        die('You are not allowed to call this page directly.');
    }

    ?>
    <div class="wrap">
        <h1><?php echo esc_html(get_admin_page_title()); ?></h1>



        <!-- Overall Analytics Dashboard -->
        <div class="q-analytics-dashboard">
            <div class="q-analytics-card">
                <h3><?php esc_html_e('Total Subscribers', 'formidable-firebase-push'); ?></h3>
                <div class="q-analytics-number"><?php echo is_array($subscribers) ? count($subscribers) : 0; ?></div>
            </div>
            <div class="q-analytics-card">
                <h3><?php esc_html_e('Total Notifications', 'formidable-firebase-push'); ?></h3>
                <div class="q-analytics-number"><?php echo esc_html($total_notifications); ?></div>
            </div>
            <div class="q-analytics-card">
                <h3><?php esc_html_e('Overall Engagement Rate', 'formidable-firebase-push'); ?></h3>
                <div class="q-analytics-number"><?php echo esc_html($engagement_rate); ?>%</div>
            </div>
        </div>

        <!-- Admin Controls -->
        <div class="q-analytics-controls">
            <button id="q-check-notifications" class="button button-primary">
                <i class="bi bi-bell"></i>
                <?php esc_html_e('Check for Unclicked Notifications', 'formidable-firebase-push'); ?>
            </button>
            <button id="q-clear-analytics" class="button button-secondary">
                <i class="bi bi-trash"></i> <?php esc_html_e('Clear Analytics Data', 'formidable-firebase-push'); ?>
            </button>
        </div>

        <!-- Per-Form Analytics -->
        <div class="q-form-analytics">
            <h2><?php esc_html_e('Form-specific Analytics', 'formidable-firebase-push'); ?></h2>

            <?php
            $form_analytics = q_get_form_analytics();
            $actions = FrmFormAction::get_action_for_form(null, 'firebase_push');

            if (!empty($actions)): ?>
                <div class="q-form-analytics-grid">
                    <?php foreach ($actions as $action):
                        $form = FrmForm::getOne($action->menu_order);
                        if (!$form)
                            continue;

                        $form_id = $action->menu_order;
                        $analytics = isset($form_analytics[$form_id]) ? $form_analytics[$form_id] : array(
                            'total_notifications' => 0,
                            'engagement_rate' => 0
                        );
                    ?>
                        <div class="q-form-analytics-card">
                            <h3><?php echo esc_html($form->name); ?></h3>
                            <div class="q-form-analytics-stats">
                                <div class="q-stat">
                                    <label><?php esc_html_e('Notifications Sent', 'formidable-firebase-push'); ?></label>
                                    <span><?php echo esc_html($analytics['total_notifications']); ?></span>
                                </div>
                                <div class="q-stat">
                                    <label><?php esc_html_e('Engagement Rate', 'formidable-firebase-push'); ?></label>
                                    <span><?php echo esc_html($analytics['engagement_rate']); ?>%</span>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <p><?php esc_html_e('No form actions configured yet.', 'formidable-firebase-push'); ?></p>
            <?php endif; ?>
        </div>

        <!-- Subscriber Management -->
        <div class="q-subscriber-controls">
            <div class="q-search-box">
                <input type="text" id="q-subscriber-search"
                    placeholder="<?php esc_attr_e('Search subscribers...', 'formidable-firebase-push'); ?>">
            </div>
            <div class="q-bulk-actions">
                <select id="q-bulk-action">
                    <option value=""><?php esc_html_e('Bulk Actions', 'formidable-firebase-push'); ?></option>
                    <option value="remove"><?php esc_html_e('Remove Selected', 'formidable-firebase-push'); ?></option>
                    <option value="test"><?php esc_html_e('Send Test Notification', 'formidable-firebase-push'); ?></option>
                </select>
                <button class="button" id="q-bulk-apply"><?php esc_html_e('Apply', 'formidable-firebase-push'); ?></button>
            </div>
        </div>

        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th class="check-column"><input type="checkbox" id="q-select-all"></th>
                    <th scope="col"><?php esc_html_e('User', 'formidable-firebase-push'); ?></th>
                    <th scope="col"><?php esc_html_e('Email', 'formidable-firebase-push'); ?></th>
                    <th scope="col"><?php esc_html_e('Devices', 'formidable-firebase-push'); ?></th>
                    <th scope="col"><?php esc_html_e('Subscribed Date', 'formidable-firebase-push'); ?></th>
                    <th scope="col"><?php esc_html_e('Last Active', 'formidable-firebase-push'); ?></th>
                    <th scope="col"><?php esc_html_e('Actions', 'formidable-firebase-push'); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($subscribers as $subscriber):
                    $last_active = get_user_meta($subscriber->ID, 'q_last_notification_interaction', true);
                    $device_count = q_get_user_device_count($subscriber->ID);
                ?>
                    <tr>
                        <td><input type="checkbox" class="q-subscriber-select" value="<?php echo esc_attr($subscriber->ID); ?>">
                        </td>
                        <td><?php echo esc_html($subscriber->display_name); ?></td>
                        <td><?php echo esc_html($subscriber->user_email); ?></td>
                        <td>
                            <span class="q-device-count"
                                title="<?php esc_attr_e('Number of registered devices', 'formidable-firebase-push'); ?>">
                                <i class="bi bi-phone"></i> <?php echo esc_html($device_count); ?>
                            </span>
                        </td>
                        <td><?php echo esc_html(date_i18n(get_option('date_format'), strtotime($subscriber->user_registered))); ?>
                        </td>
                        <td><?php echo $last_active ? esc_html(human_time_diff(strtotime($last_active))) : '-'; ?></td>
                        <td class="q-action-buttons">
                            <button class="button button-primary button-small send-test"
                                data-user-id="<?php echo esc_attr($subscriber->ID); ?>"
                                data-nonce="<?php echo esc_attr(wp_create_nonce('test_notification_' . $subscriber->ID)); ?>">
                                <i class="bi bi-bell"></i> <?php esc_html_e('Test', 'formidable-firebase-push'); ?>
                            </button>
                            <button class="button button-small remove-subscriber"
                                data-user-id="<?php echo esc_attr($subscriber->ID); ?>"
                                data-nonce="<?php echo esc_attr(wp_create_nonce('remove_subscriber_' . $subscriber->ID)); ?>">
                                <i class="bi bi-trash"></i> <?php esc_html_e('Remove', 'formidable-firebase-push'); ?>
                            </button>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>

    <style>
        .q-analytics-dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .q-analytics-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .q-analytics-number {
            font-size: 24px;
            font-weight: bold;
            color: #0384c6;
        }

        .q-subscriber-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 20px 0;
            gap: 20px;
        }

        .q-search-box {
            flex: 1;
        }

        #q-subscriber-search {
            width: 100%;
            padding: 8px;
        }

        .q-action-buttons {
            display: flex;
            gap: 5px;
        }

        .q-action-buttons button {
            padding: 4px 8px;
        }

        .bi {
            font-size: 14px;
        }

        .q-form-analytics {
            margin-top: 2em;
        }

        .q-form-analytics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1em;
            margin-top: 1em;
        }

        .q-form-analytics-card {
            background: white;
            padding: 1.5em;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .q-form-analytics-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1em;
            margin-top: 1em;
        }

        .q-stat {
            text-align: center;
        }

        .q-stat label {
            display: block;
            font-size: 0.9em;
            color: #666;
            margin-bottom: 0.5em;
        }

        .q-stat span {
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
        }

        .q-demo-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin: 20px 0;
        }

        .q-shortcode-display {
            background: #f5f5f5;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
            margin-right: 10px;
        }

        .q-demo-preview {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }

        #q-copy-shortcode {
            vertical-align: middle;
        }

        #q-copy-shortcode i {
            margin-right: 5px;
        }

        .q-analytics-controls {
            text-align: right;
            margin: -10px 0 20px 0;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        #q-clear-analytics,
        #q-check-notifications {
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }

        #q-clear-analytics .bi,
        #q-check-notifications .bi {
            font-size: 14px;
        }

        .q-device-count {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            padding: 2px 8px;
            background: #f0f0f1;
            border-radius: 12px;
            font-size: 13px;
        }

        .q-device-count .bi {
            font-size: 14px;
            color: #2271b1;
        }
    </style>

    <script type="text/javascript">
        jQuery(document).ready(function($) {
            // Search functionality
            $('#q-subscriber-search').on('input', function() {
                const searchTerm = $(this).val().toLowerCase();
                $('tbody tr').each(function() {
                    const text = $(this).text().toLowerCase();
                    $(this).toggle(text.includes(searchTerm));
                });
            });

            // Bulk selection
            $('#q-select-all').on('change', function() {
                $('.q-subscriber-select').prop('checked', $(this).prop('checked'));
            });

            // Bulk actions
            $('#q-bulk-apply').on('click', function() {
                const action = $('#q-bulk-action').val();
                const selected = $('.q-subscriber-select:checked').map(function() {
                    return $(this).val();
                }).get();

                if (!selected.length) {
                    alert('<?php esc_html_e('Please select subscribers first.', 'formidable-firebase-push'); ?>');
                    return;
                }

                switch (action) {
                    case 'remove':
                        if (confirm('<?php esc_html_e('Are you sure you want to remove the selected subscribers?', 'formidable-firebase-push'); ?>')) {
                            removeSubscribers(selected);
                        }
                        break;
                    case 'test':
                        sendTestNotifications(selected);
                        break;
                }
            });

            // Send test notification
            $('.send-test').on('click', function(e) {
                e.preventDefault();
                const button = $(this);
                const userId = button.data('user-id');
                const nonce = button.data('nonce');

                button.prop('disabled', true);

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'q_send_test_notification',
                        user_id: userId,
                        nonce: nonce
                    },
                    success: function(response) {
                        if (response.success) {
                            alert(response.data);
                        } else {
                            alert(response.data);
                        }
                        button.prop('disabled', false);
                    },
                    error: function() {
                        alert('<?php esc_html_e('An error occurred. Please try again.', 'formidable-firebase-push'); ?>');
                        button.prop('disabled', false);
                    }
                });
            });

            // Remove subscriber
            $('.remove-subscriber').on('click', function(e) {
                e.preventDefault();
                const button = $(this);
                const userId = button.data('user-id');
                const nonce = button.data('nonce');

                if (confirm('<?php esc_html_e('Are you sure you want to remove this subscription?', 'formidable-firebase-push'); ?>')) {
                    button.prop('disabled', true);

                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'q_remove_subscriber',
                            user_id: userId,
                            nonce: nonce
                        },
                        success: function(response) {
                            if (response.success) {
                                button.closest('tr').fadeOut(400, function() {
                                    $(this).remove();
                                    if ($('tbody tr').length === 0) {
                                        $('tbody').append('<tr><td colspan="6"><?php esc_html_e('No subscribers found.', 'formidable-firebase-push'); ?></td></tr>');
                                    }
                                    // Trigger a custom event that the subscription.js can listen for
                                    window.dispatchEvent(new CustomEvent('subscriptionRemoved', {
                                        detail: {
                                            userId: userId
                                        }
                                    }));
                                });
                            } else {
                                alert(response.data);
                                button.prop('disabled', false);
                            }
                        },
                        error: function() {
                            alert('<?php esc_html_e('An error occurred. Please try again.', 'formidable-firebase-push'); ?>');
                            button.prop('disabled', false);
                        }
                    });
                }
            });

            // Manual notification check functionality
            $('#q-check-notifications').on('click', function() {
                const button = $(this);
                button.prop('disabled', true);
                button.html('<i class="bi bi-hourglass-split"></i> <?php esc_html_e('Checking...', 'formidable-firebase-push'); ?>');

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'q_manual_check_notifications',
                        nonce: '<?php echo wp_create_nonce('q_manual_check_notifications'); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            alert(response.data.message);
                        } else {
                            alert(response.data || '<?php esc_html_e('An error occurred while checking notifications.', 'formidable-firebase-push'); ?>');
                        }
                        button.html('<i class="bi bi-bell"></i> <?php esc_html_e('Check for Unclicked Notifications', 'formidable-firebase-push'); ?>');
                        button.prop('disabled', false);
                    },
                    error: function() {
                        alert('<?php esc_html_e('An error occurred while checking notifications.', 'formidable-firebase-push'); ?>');
                        button.html('<i class="bi bi-bell"></i> <?php esc_html_e('Check for Unclicked Notifications', 'formidable-firebase-push'); ?>');
                        button.prop('disabled', false);
                    }
                });
            });

            // Clear Analytics functionality
            $('#q-clear-analytics').on('click', function() {
                if (!confirm('<?php esc_html_e('Are you sure you want to clear all analytics data? This action cannot be undone.', 'formidable-firebase-push'); ?>')) {
                    return;
                }

                const button = $(this);
                button.prop('disabled', true);

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'q_clear_analytics',
                        nonce: '<?php echo wp_create_nonce('q_clear_analytics'); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            // Update analytics numbers to zero
                            $('.q-analytics-number').text('0');
                            $('.q-stat span').text('0');
                            alert(response.data);
                        } else {
                            alert(response.data);
                        }
                        button.prop('disabled', false);
                    },
                    error: function() {
                        alert('<?php esc_html_e('An error occurred while clearing analytics data.', 'formidable-firebase-push'); ?>');
                        button.prop('disabled', false);
                    }
                });
            });
        });
    </script>
</div>

<!-- Notifications -->
<div class="q-pwa-tab-content" id="tab-notifications">
    <div class="q-pwa-tab-header">
        <h2>Firebase Configuration</h2>
        <p>Configure your Firebase project for push notifications. Paste your Firebase config JSON and Service Account below.</p>
    </div>
    <?php
    if (!empty($_POST['q_firebase_config_save'])) {
        check_admin_referer('q_firebase_config_save_action', 'q_firebase_config_save_nonce');
        $config = stripslashes($_POST['q_firebase_config'] ?? '');
        $service_account = stripslashes($_POST['q_firebase_service_account'] ?? '');
        $errors = [];
        // Validate Firebase Config JSON
        $config_clean = preg_replace('/const\s+firebaseConfig\s+=\s+/i', '', $config);
        $config_clean = preg_replace('/;$/', '', $config_clean);
        $decoded = json_decode($config_clean, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            $errors[] = 'Firebase configuration must be valid JSON.';
        } else {
            $required_fields = ['apiKey', 'authDomain', 'projectId', 'storageBucket', 'messagingSenderId', 'appId'];
            foreach ($required_fields as $field) {
                if (empty($decoded[$field])) {
                    $errors[] = "Firebase configuration is missing required field: {$field}";
                }
            }
        }
        // Validate Service Account JSON
        $service_decoded = json_decode($service_account, true);
        if (!empty($service_account)) {
            if (json_last_error() !== JSON_ERROR_NONE) {
                $errors[] = 'Service Account must be valid JSON.';
            } else {
                $required_sa_fields = ['type', 'project_id', 'private_key_id', 'private_key', 'client_email', 'client_id'];
                foreach ($required_sa_fields as $field) {
                    if (empty($service_decoded[$field])) {
                        $errors[] = "Service Account JSON is missing required field: {$field}";
                    }
                }
            }
        }
        if (empty($errors)) {
            update_option('q_firebase_config', json_encode($decoded, JSON_PRETTY_PRINT));
            update_option('q_firebase_service_account', $service_account);
            echo '<div class="notice notice-success is-dismissible"><p>Firebase configuration saved successfully.</p></div>';
        } else {
            echo '<div class="notice notice-error"><ul><li>' . implode('</li><li>', array_map('esc_html', $errors)) . '</li></ul></div>';
        }
    }
    $firebase_config = get_option('q_firebase_config');
    $service_account = get_option('q_firebase_service_account');
    ?>
    <form method="post" style="max-width: 700px; margin-bottom: 30px;">
        <?php wp_nonce_field('q_firebase_config_save_action', 'q_firebase_config_save_nonce'); ?>
        <table class="form-table" role="presentation">
            <tr>
                <th scope="row"><label for="q_firebase_config">Firebase Config JSON</label></th>
                <td>
                    <textarea name="q_firebase_config" id="q_firebase_config" rows="8" cols="60" class="large-text code"><?php echo esc_textarea($firebase_config); ?></textarea>
                    <p class="description">Paste your Firebase configuration as JSON. Example:<br>
                    <pre style="background: #f0f0f0; padding: 10px;">{
    "apiKey": "your-api-key",
    "authDomain": "your-project.firebaseapp.com",
    "projectId": "your-project",
    "storageBucket": "your-project.appspot.com",
    "messagingSenderId": "*********",
    "appId": "1:*********:web:abcdef",
    "measurementId": "G-ABCDEF123"
}</pre>
                    </p>
                </td>
            </tr>
            <tr>
                <th scope="row"><label for="q_firebase_service_account">Service Account JSON</label></th>
                <td>
                    <textarea name="q_firebase_service_account" id="q_firebase_service_account" rows="8" cols="60" class="large-text code"><?php echo esc_textarea($service_account); ?></textarea>
                    <p class="description">Paste your Firebase Service Account JSON here. To get this:<br>
                    <ol>
                        <li>Go to Firebase Console → Project Settings → Service Accounts</li>
                        <li>Click "Generate New Private Key"</li>
                        <li>Copy the contents of the downloaded JSON file here</li>
                    </ol>
                    </p>
                </td>
            </tr>
        </table>
        <p><input type="submit" name="q_firebase_config_save" class="button button-primary" value="Save Firebase Settings"></p>
    </form>
</div>

<style>
    /* Shortcode Reference Styles */
    .q-pwa-shortcode-section {
        margin-bottom: 40px;
    }

    .q-pwa-shortcode-section h3 {
        color: #0073aa;
        font-size: 1.3em;
        margin-bottom: 20px;
        background: #fff;
    }

    .q-pwa-shortcode-table {
        background: #fff;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .q-pwa-shortcode-table th {
        background: #1d2327;
        color: #fff !important;
        font-weight: 600;
        padding: 15px 12px;
        text-align: left;
    }

    .q-pwa-shortcode-table td {
        padding: 15px 12px;
        vertical-align: top;
        border-bottom: 1px solid #f0f0f0;
    }

    .q-pwa-shortcode-table tr:last-child td {
        border-bottom: none;
    }

    .q-pwa-shortcode-table tr:hover {
        background: #f8f9fa;
    }

    .q-pwa-shortcode-table code {
        background: #f1f3f6;
        color: #d63384;
        padding: 3px 6px;
        border-radius: 4px;
        font-size: 0.9em;
        font-weight: 600;
    }

    .q-pwa-shortcode-table small {
        color: #666;
        line-height: 1.4;
        display: block;
        margin-top: 5px;
    }

    .q-pwa-shortcode-table small strong {
        color: #333;
        font-weight: 600;
    }

    .q-pwa-shortcode-notes {
        background: #fff;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 20px;
        margin-top: 30px;
    }

    .q-pwa-shortcode-notes h4 {
        color: #0073aa;
        margin-top: 0;
        margin-bottom: 15px;
        font-size: 1.2em;
    }

    .q-pwa-shortcode-notes ul {
        margin: 0;
        padding-left: 20px;
    }

    .q-pwa-shortcode-notes li {
        margin-bottom: 10px;
        line-height: 1.5;
    }

    .q-pwa-shortcode-notes li:last-child {
        margin-bottom: 0;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {

        .q-pwa-shortcode-table th,
        .q-pwa-shortcode-table td {
            padding: 10px 8px;
            font-size: 0.9em;
        }

        .q-pwa-shortcode-section h3 {
            font-size: 1.1em;
            padding: 8px 12px;
        }
    }
</style>