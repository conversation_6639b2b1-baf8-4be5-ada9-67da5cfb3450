<?php
if (!defined('ABSPATH')) {
    exit;
}

class Q_PWA_Settings
{
    public static function init()
    {
        add_action('admin_menu', [self::class, 'add_pwa_settings_page']);
        add_action('admin_init', [self::class, 'register_pwa_settings']);
        add_action('wp_enqueue_scripts', [self::class, 'enqueue_pwa_scripts']);
        add_action('wp_head', [self::class, 'add_pwa_meta_tags']);
        add_action('wp_head', [self::class, 'add_manifest_link']);
        add_action('admin_enqueue_scripts', [self::class, 'enqueue_pwa_scripts']);

        // Flush manifest cache when any PWA setting is updated
        add_action('updated_option', [self::class, 'maybe_flush_manifest_cache'], 10, 3);

        // Remove default WordPress site icon when PWA icon is provided so it doesn't override the PWA icon
        add_action('wp_head', [self::class, 'remove_site_icon_meta'], 1);
    }

    public static function add_pwa_settings_page()
    {
        add_submenu_page(
            'options-general.php',
            'PWA Settings',
            'P<PERSON> Settings',
            'manage_options',
            'q-pwa-settings',
            [self::class, 'render_pwa_settings_page']
        );
    }

    public static function register_pwa_settings()
    {
        // PWA Basic Settings
        register_setting('q_pwa_settings', 'q_pwa_enabled', [
            'type' => 'boolean',
            'default' => false,
            'sanitize_callback' => 'rest_sanitize_boolean'
        ]);

        // Analytics Settings
        register_setting('q_pwa_settings', 'q_pwa_analytics_enabled', [
            'type' => 'boolean',
            'default' => true,
            'sanitize_callback' => 'rest_sanitize_boolean'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_analytics_retention', [
            'type' => 'integer',
            'default' => 90, // days
            'sanitize_callback' => 'absint'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_analytics_refresh_interval', [
            'type' => 'integer',
            'default' => 30, // seconds
            'sanitize_callback' => 'absint'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_app_name', [
            'type' => 'string',
            'default' => get_bloginfo('name'),
            'sanitize_callback' => 'sanitize_text_field'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_app_short_name', [
            'type' => 'string',
            'default' => get_bloginfo('name'),
            'sanitize_callback' => 'sanitize_text_field'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_app_description', [
            'type' => 'string',
            'default' => get_bloginfo('description'),
            'sanitize_callback' => 'sanitize_textarea_field'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_theme_color', [
            'type' => 'string',
            'default' => '#000000',
            'sanitize_callback' => 'sanitize_hex_color'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_background_color', [
            'type' => 'string',
            'default' => '#ffffff',
            'sanitize_callback' => 'sanitize_hex_color'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_display_mode', [
            'type' => 'string',
            'default' => 'standalone',
            'sanitize_callback' => [self::class, 'sanitize_display_mode']
        ]);

        register_setting('q_pwa_settings', 'q_pwa_orientation', [
            'type' => 'string',
            'default' => 'any',
            'sanitize_callback' => [self::class, 'sanitize_orientation']
        ]);

        register_setting('q_pwa_settings', 'q_pwa_start_url', [
            'type' => 'string',
            'default' => '/',
            'sanitize_callback' => 'esc_url_raw'
        ]);

        // Simplified Icon Settings
        register_setting('q_pwa_settings', 'q_pwa_icon', [
            'type' => 'string',
            'default' => '',
            'sanitize_callback' => 'esc_url_raw'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_splash', [
            'type' => 'string',
            'default' => '',
            'sanitize_callback' => 'esc_url_raw'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_ios_status_bar_style', [
            'type' => 'string',
            'default' => 'default',
            'sanitize_callback' => [self::class, 'sanitize_ios_status_bar_style']
        ]);

        register_setting('q_pwa_settings', 'q_pwa_ios_web_app_capable', [
            'type' => 'boolean',
            'default' => true,
            'sanitize_callback' => 'rest_sanitize_boolean'
        ]);


        // Android-specific Settings
        register_setting('q_pwa_settings', 'q_pwa_maskable_icon', [
            'type' => 'string',
            'default' => '',
            'sanitize_callback' => 'esc_url_raw'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_nav_color', [
            'type' => 'string',
            'default' => '#000000',
            'sanitize_callback' => 'sanitize_hex_color'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_monochrome_icon', [
            'type' => 'boolean',
            'default' => false,
            'sanitize_callback' => 'rest_sanitize_boolean'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_monochrome_icon_url', [
            'type' => 'string',
            'default' => '',
            'sanitize_callback' => 'esc_url_raw'
        ]);

        // App Shortcuts Settings
        register_setting('q_pwa_settings', 'q_pwa_shortcuts_enabled', [
            'type' => 'boolean',
            'default' => false,
            'sanitize_callback' => 'rest_sanitize_boolean'
        ]);

        // Register shortcut settings (up to 4 shortcuts)
        for ($i = 1; $i <= 4; $i++) {
            register_setting('q_pwa_settings', "q_pwa_shortcut_{$i}_name", [
                'type' => 'string',
                'default' => '',
                'sanitize_callback' => 'sanitize_text_field'
            ]);

            register_setting('q_pwa_settings', "q_pwa_shortcut_{$i}_url", [
                'type' => 'string',
                'default' => '',
                'sanitize_callback' => 'esc_url_raw'
            ]);

            register_setting('q_pwa_settings', "q_pwa_shortcut_{$i}_description", [
                'type' => 'string',
                'default' => '',
                'sanitize_callback' => 'sanitize_text_field'
            ]);

            register_setting('q_pwa_settings', "q_pwa_shortcut_{$i}_icon", [
                'type' => 'string',
                'default' => '',
                'sanitize_callback' => 'esc_url_raw'
            ]);
        }

        // Desktop-specific Settings
        register_setting('q_pwa_settings', 'q_pwa_window_controls_overlay', [
            'type' => 'boolean',
            'default' => false,
            'sanitize_callback' => 'rest_sanitize_boolean'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_wco_caption_button_close', [
            'type' => 'string',
            'default' => '',
            'sanitize_callback' => 'sanitize_text_field'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_protocol_handlers_enabled', [
            'type' => 'boolean',
            'default' => false,
            'sanitize_callback' => 'rest_sanitize_boolean'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_protocol_list', [
            'type' => 'string',
            'default' => '',
            'sanitize_callback' => 'sanitize_text_field'
        ]);

        // Advanced Manifest Features
        register_setting('q_pwa_settings', 'q_pwa_categories', [
            'type' => 'string',
            'default' => '',
            'sanitize_callback' => 'sanitize_text_field'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_scope', [
            'type' => 'string',
            'default' => '/',
            'sanitize_callback' => 'esc_url_raw'
        ]);


        register_setting('q_pwa_settings', 'q_pwa_android_package', [
            'type' => 'string',
            'default' => '',
            'sanitize_callback' => 'sanitize_text_field'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_ios_app_id', [
            'type' => 'string',
            'default' => '',
            'sanitize_callback' => 'sanitize_text_field'
        ]);

        // Share Target Settings
        register_setting('q_pwa_settings', 'q_pwa_share_target_enabled', [
            'type' => 'boolean',
            'default' => false,
            'sanitize_callback' => 'rest_sanitize_boolean'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_share_target_url', [
            'type' => 'string',
            'default' => '/share',
            'sanitize_callback' => 'esc_url_raw'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_share_files_enabled', [
            'type' => 'boolean',
            'default' => false,
            'sanitize_callback' => 'rest_sanitize_boolean'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_share_file_types', [
            'type' => 'string',
            'default' => 'image/*,text/*',
            'sanitize_callback' => 'sanitize_text_field'
        ]);

        // File Handlers Settings
        register_setting('q_pwa_settings', 'q_pwa_file_handlers_enabled', [
            'type' => 'boolean',
            'default' => false,
            'sanitize_callback' => 'rest_sanitize_boolean'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_file_handler_types', [
            'type' => 'string',
            'default' => 'text/plain,text/csv',
            'sanitize_callback' => 'sanitize_text_field'
        ]);

        // Display Override Settings
        register_setting('q_pwa_settings', 'q_pwa_display_override', [
            'type' => 'string',
            'default' => '',
            'sanitize_callback' => 'sanitize_text_field'
        ]);

        // Additional Icon Settings (192x192 and 512x512) & Apple Touch Icon
        // These options are used in the settings UI but were not previously registered, so they were not being saved.
        register_setting('q_pwa_settings', 'q_pwa_icon_192', [
            'type' => 'string',
            'default' => '',
            'sanitize_callback' => 'esc_url_raw'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_icon_512', [
            'type' => 'string',
            'default' => '',
            'sanitize_callback' => 'esc_url_raw'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_apple_touch_icon', [
            'type' => 'string',
            'default' => '',
            'sanitize_callback' => 'esc_url_raw'
        ]);

        // Offline Fallback Page Setting
        register_setting('q_pwa_settings', 'q_pwa_offline_page_id', [
            'type' => 'integer',
            'default' => 0,
            'sanitize_callback' => 'absint'
        ]);

        // Caching Strategy Setting
        register_setting('q_pwa_settings', 'q_pwa_caching_strategy', [
            'type' => 'string',
            'default' => 'network-first',
            'sanitize_callback' => function ($value) {
                $allowed = ['cache-first', 'network-first', 'stale-while-revalidate'];
                return in_array($value, $allowed) ? $value : 'network-first';
            }
        ]);

        // Desktop-specific Settings
        register_setting('q_pwa_settings', 'q_pwa_installable_on_desktop', [
            'type' => 'boolean',
            'default' => false,
            'sanitize_callback' => 'rest_sanitize_boolean'
        ]);

        // Add settings sections
        add_settings_section(
            'q_pwa_basic_section',
            'Basic PWA Settings',
            [self::class, 'render_basic_section_description'],
            'q_pwa_settings'
        );

        add_settings_section(
            'q_pwa_appearance_section',
            'Appearance Settings',
            [self::class, 'render_appearance_section_description'],
            'q_pwa_settings'
        );

        add_settings_section(
            'q_pwa_icons_section',
            'App Icons',
            [self::class, 'render_icons_section_description'],
            'q_pwa_settings'
        );

        add_settings_section(
            'q_pwa_advanced_section',
            'Advanced Features',
            [self::class, 'render_advanced_section_description'],
            'q_pwa_settings'
        );

        add_settings_section(
            'q_pwa_analytics_section',
            'Analytics Settings',
            [self::class, 'render_analytics_section_description'],
            'q_pwa_settings'
        );

        // Add analytics settings fields
        add_settings_field(
            'q_pwa_analytics_enabled',
            'Enable Analytics',
            [self::class, 'render_checkbox_field'],
            'q_pwa_settings',
            'q_pwa_analytics_section',
            ['field' => 'q_pwa_analytics_enabled', 'description' => 'Collect usage data for your PWA']
        );

        add_settings_field(
            'q_pwa_analytics_retention',
            'Data Retention Period',
            [self::class, 'render_select_field'],
            'q_pwa_settings',
            'q_pwa_analytics_section',
            [
                'field' => 'q_pwa_analytics_retention',
                'options' => [
                    '30' => '30 Days',
                    '60' => '60 Days',
                    '90' => '90 Days',
                    '180' => '180 Days',
                    '365' => '1 Year'
                ],
                'description' => 'How long to keep analytics data before automatic deletion'
            ]
        );

        // Add settings fields
        self::add_settings_fields();

        // Add Windows Control Overlay settings fields
        self::add_wco_settings_fields();

        // Add offline fallback page field
        add_settings_field(
            'q_pwa_offline_page_id',
            'Offline Fallback Page',
            [self::class, 'render_page_dropdown_field'],
            'q_pwa_settings',
            'q_pwa_basic_section',
            [
                'field' => 'q_pwa_offline_page_id',
                'description' => 'Select the WordPress page to show when offline.'
            ]
        );

        // Add caching strategy field
        add_settings_field(
            'q_pwa_caching_strategy',
            'Caching Strategy',
            [self::class, 'render_select_field'],
            'q_pwa_settings',
            'q_pwa_basic_section',
            [
                'field' => 'q_pwa_caching_strategy',
                'options' => [
                    'cache-first' => 'Cache First',
                    'network-first' => 'Network First',
                    'stale-while-revalidate' => 'Stale While Revalidate'
                ],
                'description' => 'Choose how the service worker should cache resources.'
            ]
        );
    }

    /**
     * Add Windows Control Overlay settings fields
     */
    private static function add_wco_settings_fields()
    {
        // Windows Control Overlay settings
        add_settings_field(
            'q_pwa_window_controls_overlay',
            'Enable Windows Control Overlay',
            [self::class, 'render_checkbox_field'],
            'q_pwa_settings',
            'q_pwa_advanced_section',
            ['field' => 'q_pwa_window_controls_overlay', 'description' => 'Enable custom title bar for PWA on Windows (desktop PWA only)']
        );

        add_settings_field(
            'q_pwa_wco_caption_button_close',
            'Caption Button Close Text',
            [self::class, 'render_text_field'],
            'q_pwa_settings',
            'q_pwa_advanced_section',
            ['field' => 'q_pwa_wco_caption_button_close', 'description' => 'Optional text for the close button in the title bar (leave empty for default)']
        );
    }

    private static function add_settings_fields()
    {
        // Basic Settings Fields
        add_settings_field(
            'q_pwa_enabled',
            'Enable PWA',
            [self::class, 'render_checkbox_field'],
            'q_pwa_settings',
            'q_pwa_basic_section',
            ['field' => 'q_pwa_enabled', 'description' => 'Enable Progressive Web App functionality']
        );

        add_settings_field(
            'q_pwa_app_name',
            'App Name',
            [self::class, 'render_text_field'],
            'q_pwa_settings',
            'q_pwa_basic_section',
            ['field' => 'q_pwa_app_name', 'description' => 'Full name of your app (max 45 characters)']
        );

        add_settings_field(
            'q_pwa_app_short_name',
            'Short Name',
            [self::class, 'render_text_field'],
            'q_pwa_settings',
            'q_pwa_basic_section',
            ['field' => 'q_pwa_app_short_name', 'description' => 'Short name for home screen (max 12 characters)']
        );

        add_settings_field(
            'q_pwa_app_description',
            'Description',
            [self::class, 'render_textarea_field'],
            'q_pwa_settings',
            'q_pwa_basic_section',
            ['field' => 'q_pwa_app_description', 'description' => 'Brief description of your app']
        );

        add_settings_field(
            'q_pwa_start_url',
            'Start URL',
            [self::class, 'render_text_field'],
            'q_pwa_settings',
            'q_pwa_basic_section',
            ['field' => 'q_pwa_start_url', 'description' => 'URL to load when app is launched (relative to site root)']
        );

        // Appearance Settings Fields
        add_settings_field(
            'q_pwa_theme_color',
            'Theme Color',
            [self::class, 'render_color_field'],
            'q_pwa_settings',
            'q_pwa_appearance_section',
            ['field' => 'q_pwa_theme_color', 'description' => 'Primary theme color for the app']
        );

        add_settings_field(
            'q_pwa_background_color',
            'Background Color',
            [self::class, 'render_color_field'],
            'q_pwa_settings',
            'q_pwa_appearance_section',
            ['field' => 'q_pwa_background_color', 'description' => 'Background color for splash screen']
        );

        add_settings_field(
            'q_pwa_display_mode',
            'Display Mode',
            [self::class, 'render_select_field'],
            'q_pwa_settings',
            'q_pwa_appearance_section',
            [
                'field' => 'q_pwa_display_mode',
                'options' => [
                    'standalone' => 'Standalone (Recommended)',
                    'fullscreen' => 'Fullscreen',
                    'minimal-ui' => 'Minimal UI',
                    'browser' => 'Browser'
                ],
                'description' => 'How the app should be displayed when launched'
            ]
        );

        add_settings_field(
            'q_pwa_orientation',
            'Orientation',
            [self::class, 'render_select_field'],
            'q_pwa_settings',
            'q_pwa_appearance_section',
            [
                'field' => 'q_pwa_orientation',
                'options' => [
                    'any' => 'Any',
                    'portrait' => 'Portrait',
                    'landscape' => 'Landscape'
                ],
                'description' => 'Preferred screen orientation'
            ]
        );

        // Icon Settings Fields (remain in their original section)
        add_settings_field(
            'q_pwa_icon_192',
            '192x192 Icon',
            [self::class, 'render_media_field'],
            'q_pwa_settings',
            'q_pwa_icons_section',
            ['field' => 'q_pwa_icon_192', 'description' => 'App icon for Android devices (192x192 pixels)']
        );

        add_settings_field(
            'q_pwa_icon_512',
            '512x512 Icon',
            [self::class, 'render_media_field'],
            'q_pwa_settings',
            'q_pwa_icons_section',
            ['field' => 'q_pwa_icon_512', 'description' => 'App icon for splash screen (512x512 pixels)']
        );

        // Desktop Settings Fields
        add_settings_field(
            'q_pwa_installable_on_desktop',
            'Allow Install on Desktop',
            [self::class, 'render_radio_field'],
            'q_pwa_settings',
            'q_pwa_advanced_section',
            [
                'field' => 'q_pwa_installable_on_desktop',
                'options' => [
                    1 => 'Yes',
                    0 => 'No',
                ],
                'description' => 'Allow users to install the PWA on desktop devices.'
            ]
        );
    }

    public static function render_pwa_settings_page()
    {
        // Inject variables for the subscribers tab
        $subscribers = get_users(array(
            'meta_key' => 'q_push_token',
            'meta_value' => '',
            'meta_compare' => '!='
        ));
        $total_notifications = function_exists('q_get_total_notifications') ? q_get_total_notifications() : 0;
        $engagement_rate = function_exists('q_get_engagement_rate') ? q_get_engagement_rate() : 0;
        require_once Q_PLUGIN_DIR . 'includes/templates/pwa-app-layout.php';
    }

    // Section descriptions
    public static function render_basic_section_description()
    {
        echo '<p>Configure basic Progressive Web App settings for your site.</p>';
    }

    public static function render_appearance_section_description()
    {
        echo '<p>Customize how your PWA looks and behaves when installed.</p>';
    }

    public static function render_icons_section_description()
    {
        echo '<p>Upload icons for your PWA. Icons should be square and in PNG format.</p>';
    }

    public static function render_advanced_section_description()
    {
        echo '<p>Configure advanced PWA features like app shortcuts and protocol handlers.</p>';
    }

    public static function render_analytics_section_description()
    {
        echo '<p>Configure settings related to PWA analytics and view usage statistics.</p>';
    }

    // Field renderers
    public static function render_text_field($args)
    {
        $field = $args['field'];
        $value = get_option($field, '');
        $description = $args['description'] ?? '';

        echo '<input type="text" name="' . esc_attr($field) . '" value="' . esc_attr($value) . '" class="regular-text" />';
        if ($description) {
            echo '<p class="description">' . esc_html($description) . '</p>';
        }
    }

    public static function render_textarea_field($args)
    {
        $field = $args['field'];
        $value = get_option($field, '');
        $description = $args['description'] ?? '';

        echo '<textarea name="' . esc_attr($field) . '" rows="3" cols="50" class="large-text">' . esc_textarea($value) . '</textarea>';
        if ($description) {
            echo '<p class="description">' . esc_html($description) . '</p>';
        }
    }

    public static function render_checkbox_field($args)
    {
        $field       = $args['field'];
        $value       = get_option($field, false);
        $description = $args['description'] ?? '';

        // Ensure the option gets updated when the box is unchecked by adding a hidden fallback field.
        echo '<input type="hidden" name="' . esc_attr($field) . '" value="0" />';

        // Render the actual checkbox.
        echo '<label style="display:inline-flex;align-items:center;gap:6px;">';
        echo '<input type="checkbox" name="' . esc_attr($field) . '" value="1" ' . checked(1, $value, false) . ' />';
        if ($description) {
            echo '<span>' . esc_html($description) . '</span>';
        }
        echo '</label>';
    }

    public static function render_color_field($args)
    {
        $field = $args['field'];
        $value = get_option($field, '#000000');
        $description = $args['description'] ?? '';

        echo '<input type="color" name="' . esc_attr($field) . '" value="' . esc_attr($value) . '" />';
        if ($description) {
            echo '<p class="description">' . esc_html($description) . '</p>';
        }
    }

    public static function render_select_field($args)
    {
        $field = $args['field'];
        $value = get_option($field, '');
        $options = $args['options'] ?? [];
        $description = $args['description'] ?? '';

        echo '<select name="' . esc_attr($field) . '">';
        foreach ($options as $option_value => $option_label) {
            echo '<option value="' . esc_attr($option_value) . '" ' . selected($value, $option_value, false) . '>' . esc_html($option_label) . '</option>';
        }
        echo '</select>';
        if ($description) {
            echo '<p class="description">' . esc_html($description) . '</p>';
        }
    }

    public static function render_media_field($args)
    {
        $field = $args['field'];
        $value = get_option($field, '');
        $description = $args['description'] ?? '';

        echo '<input type="url" name="' . esc_attr($field) . '" value="' . esc_attr($value) . '" class="regular-text" />';
        echo '<button type="button" class="button q-media-upload" data-field="' . esc_attr($field) . '">Upload Image</button>';
        if ($value) {
            echo '<br><img src="' . esc_url($value) . '" style="max-width: 100px; max-height: 100px; margin-top: 10px;" />';
        }
        if ($description) {
            echo '<p class="description">' . esc_html($description) . '</p>';
        }
    }

    /**
     * Render a dropdown of WordPress pages for selecting the offline fallback page
     */
    public static function render_page_dropdown_field($args)
    {
        $field = $args['field'];
        $value = get_option($field, 0);
        $description = $args['description'] ?? '';
        $pages = get_pages(['post_status' => 'publish']);
        echo '<select name="' . esc_attr($field) . '">';
        echo '<option value="0">-- None --</option>';
        foreach ($pages as $page) {
            $selected = selected($value, $page->ID, false);
            echo '<option value="' . esc_attr($page->ID) . '" ' . $selected . '>' . esc_html($page->post_title) . '</option>';
        }
        echo '</select>';
        if ($description) {
            echo '<p class="description">' . esc_html($description) . '</p>';
        }
    }

    // Sanitization functions
    public static function sanitize_display_mode($value)
    {
        $allowed = ['standalone', 'fullscreen', 'minimal-ui', 'browser'];
        return in_array($value, $allowed) ? $value : 'standalone';
    }

    public static function sanitize_orientation($value)
    {
        $allowed = ['any', 'portrait', 'landscape'];
        return in_array($value, $allowed) ? $value : 'any';
    }

    // Frontend functionality

    public static function add_pwa_meta_tags()
    {
        if (!get_option('q_pwa_enabled', false)) {
            return;
        }

        $theme_color = get_option('q_pwa_theme_color', '#000000');
        $app_name = get_option('q_pwa_app_name', get_bloginfo('name'));

        echo '<meta name="theme-color" content="' . esc_attr($theme_color) . '">' . "\n";

        // --------------------------------------------------
        // iOS specific meta tags (configurable via settings)
        // --------------------------------------------------
        $ios_capable = get_option('q_pwa_ios_web_app_capable', false);
        if ($ios_capable) {
            echo '<meta name="apple-mobile-web-app-capable" content="yes">' . "\n";
        }

        $ios_status_bar = get_option('q_pwa_ios_status_bar_style', 'default');
        echo '<meta name="apple-mobile-web-app-status-bar-style" content="' . esc_attr($ios_status_bar) . '">' . "\n";
        echo '<meta name="apple-mobile-web-app-title" content="' . esc_attr($app_name) . '">' . "\n";

        // Android / generic Meta
        echo '<meta name="mobile-web-app-capable" content="yes">' . "\n";

        // Navigation bar colour (Android 8.0+ & Windows task-bar) if different from theme colour
        $nav_color = get_option('q_pwa_nav_color', '');
        if (!empty($nav_color)) {
            echo '<meta name="msapplication-navbutton-color" content="' . esc_attr($nav_color) . '">' . "\n";
        }

        echo '<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">' . "\n";

        // Add Windows Control Overlay meta tag if enabled
        if (get_option('q_pwa_window_controls_overlay', false)) {
            echo '<meta name="theme-color" media="(prefers-color-scheme: light)" content="' . esc_attr($theme_color) . '">' . "\n";
            echo '<meta name="theme-color" media="(prefers-color-scheme: dark)" content="' . esc_attr($theme_color) . '">' . "\n";
        }

        // Add Apple touch icon if available or fallback to main icon
        $apple_touch_icon = get_option('q_pwa_apple_touch_icon', '');
        $main_icon = get_option('q_pwa_icon', '');
        $touch_icon_src = !empty($apple_touch_icon) ? $apple_touch_icon : $main_icon;
        if (!empty($touch_icon_src)) {
            echo '<link rel="apple-touch-icon" href="' . esc_url($touch_icon_src) . '">' . "\n";
        }

        // --------------------------------------------------
        // Favicon / generic icon links (desktop browsers)
        // --------------------------------------------------
        if (!empty($main_icon)) {
            $icon32 = esc_url($main_icon);
            echo '<link rel="icon" type="image/png" sizes="32x32" href="' . $icon32 . '">' . "\n";
            // Use 192x192 if provided
            $icon192 = get_option('q_pwa_icon_192', $main_icon);
            if (!empty($icon192)) {
                if (!preg_match('/^https?:\/\//', $icon192)) {
                    $icon192 = site_url($icon192);
                }
                echo '<link rel="icon" type="image/png" sizes="192x192" href="' . esc_url($icon192) . '">' . "\n";
            }
        }
    }

    public static function add_manifest_link()
    {
        if (!get_option('q_pwa_enabled', false)) {
            return;
        }

        // Add a cache-busting parameter using the manifest update timestamp
        $version = get_option('q_pwa_manifest_updated', time());
        $cache_buster = '?v=' . $version;

        echo '<link rel="manifest" href="' . esc_url(home_url('/manifest.json' . $cache_buster)) . '">' . "\n";

        // Add a script to force manifest reload
        echo '<script>
        if ("serviceWorker" in navigator) {
            window.addEventListener("load", function() {
                // Force manifest reload
                if (navigator.serviceWorker.controller) {
                    try {
                        navigator.serviceWorker.controller.postMessage({
                            type: "CLEAR_MANIFEST_CACHE",
                            version: "' . $version . '"
                        });
                    } catch(e) {
                        console.log("Error sending message to service worker:", e);
                    }
                }
                
                // Fetch the manifest directly to ensure it\'s updated in the browser cache
                fetch("/manifest.json' . $cache_buster . '", {
                    cache: "no-cache",
                    headers: {
                        "Cache-Control": "no-cache, no-store, must-revalidate",
                        "Pragma": "no-cache",
                        "Expires": "0"
                    }
                }).then(function(response) {
                    console.log("Manifest refreshed:", response.status);
                    // Force PWA to update
                    if ("serviceWorker" in navigator && navigator.serviceWorker.controller) {
                        navigator.serviceWorker.controller.postMessage({
                            type: "SKIP_WAITING"
                        });
                    }
                }).catch(function(err) {
                    console.log("Error fetching manifest:", err);
                });
            });
        }
        </script>' . "\n";
    }

    // Check PWA requirements
    public static function check_pwa_requirements()
    {
        $requirements = [
            'https' => is_ssl(),
            'manifest' => get_option('q_pwa_enabled', false),
            'service_worker' => file_exists(ABSPATH . 'firebase-messaging-sw.js'),
            'icons' => !empty(get_option('q_pwa_icon', ''))
                || (!empty(get_option('q_pwa_icon_192', '')) && !empty(get_option('q_pwa_icon_512', '')))
                || !empty(get_option('site_icon'))
        ];

        return $requirements;
    }

    // Get PWA status
    public static function get_pwa_status()
    {
        $requirements = self::check_pwa_requirements();
        $total = count($requirements);
        $met = count(array_filter($requirements));

        return [
            'requirements' => $requirements,
            'percentage' => round(($met / $total) * 100),
            'ready' => $met === $total
        ];
    }

    // Enqueue admin scripts and styles
    public static function enqueue_pwa_scripts($hook)
    {
        // For frontend, enqueue PWA scripts regardless of page
        if (!is_admin()) {
            // Enqueue PWA styles
            wp_enqueue_style(
                'q-pwa-styles',
                plugins_url('includes/css/pwa-styles.css', dirname(__FILE__)),
                array(),
                filemtime(Q_PLUGIN_DIR . 'includes/css/pwa-styles.css')
            );


            // Enqueue PWA manager script
            wp_enqueue_script(
                'q-pwa-manager',
                plugins_url('includes/js/pwa-manager.js', dirname(__FILE__)),
                array('jquery'),
                filemtime(Q_PLUGIN_DIR . 'includes/js/pwa-manager.js'),
                true
            );

            // Enqueue analytics tracking script if enabled
            if (get_option('q_pwa_analytics_enabled', true)) {
                wp_enqueue_script(
                    'q-pwa-analytics-tracking',
                    plugins_url('includes/js/pwa-analytics-tracking.js', dirname(__FILE__)),
                    array('q-pwa-manager'),
                    filemtime(Q_PLUGIN_DIR . 'includes/js/pwa-analytics-tracking.js'),
                    true
                );
            }

            // Enqueue PWA shortcuts handler
            if (get_option('q_pwa_shortcuts_enabled', false)) {
                wp_enqueue_script(
                    'q-pwa-shortcuts',
                    plugins_url('includes/js/pwa-shortcuts.js', dirname(__FILE__)),
                    array('q-pwa-manager'),
                    filemtime(Q_PLUGIN_DIR . 'includes/js/pwa-shortcuts.js'),
                    true
                );
            }



            // ------------------------------------------------------------
            // Pass PWA settings to JavaScript
            // ------------------------------------------------------------
            // Ensure we have an absolute URL for the main PWA icon so it
            // can be used inside in-browser install banners.
            $icon_url = get_option('q_pwa_icon', '');
            if ($icon_url && !preg_match('/^https?:\/\//', $icon_url)) {
                $icon_url = site_url($icon_url);
            }

            wp_localize_script('q-pwa-manager', 'qPWASettings', [
                'enabled' => (bool) get_option('q_pwa_enabled', false),
                'appName' => get_option('q_pwa_app_name', get_bloginfo('name')),
                'themeColor' => get_option('q_pwa_theme_color', '#000000'),
                'backgroundColor' => get_option('q_pwa_background_color', '#ffffff'),
                'cacheVersion' => get_option('q_pwa_cache_version', 'v1'),
                'cacheTTL' => 3600 * 24 * 7, // 1 week default
                'siteUrl' => home_url('/'),
                'ajaxUrl' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('q_pwa_nonce'),
                'windowControlsOverlay' => (bool) get_option('q_pwa_window_controls_overlay', false),
                'wcoCaptionButtonClose' => '',
                'analyticsEnabled' => (bool) get_option('q_pwa_analytics_enabled', true),
                'iconUrl' => $icon_url,
                'appVersion' => self::get_plugin_version(),
                'currentUserId' => get_current_user_id(), // Add WordPress user ID for analytics tracking
                'installableOnDesktop' => (bool) get_option('q_pwa_installable_on_desktop', false),
                'currentUserId' => get_current_user_id() // Add WordPress user ID for analytics tracking
            ]);

            return;
        }

        // Admin scripts
        if ($hook === 'settings_page_q-pwa-settings') {
            // Ensure WordPress media library scripts are available for wp.media usage.
            // This must be loaded before our custom admin script so that wp.media is defined.
            if (function_exists('wp_enqueue_media')) {
                wp_enqueue_media();
            }

            // Enqueue analytics scripts and styles if on analytics tab
            if (isset($_GET['tab']) && $_GET['tab'] === 'analytics') {

                // Analytics JS
                wp_enqueue_script(
                    'q-pwa-analytics',
                    plugins_url('includes/js/pwa-analytics.js', dirname(__FILE__)),
                    array('jquery'),
                    filemtime(Q_PLUGIN_DIR . 'includes/js/pwa-analytics.js'),
                    true
                );

                // Pass analytics data to JS
                wp_localize_script('q-pwa-analytics', 'qPWAAnalytics', [
                    'ajaxUrl' => admin_url('admin-ajax.php'),
                    'nonce' => wp_create_nonce('q_pwa_analytics_nonce'),
                    'refreshInterval' => get_option('q_pwa_analytics_refresh_interval', 30) // seconds
                ]);
            }
        }

        // Only load admin scripts on PWA settings page
        if ($hook !== 'settings_page_q-pwa-settings') {
            return;
        }

        // Verify script dependencies are registered
        if (!wp_script_is('jquery', 'registered')) {
            error_log('PWA Admin: jQuery dependency missing');
            return;
        }

        // Enqueue admin CSS with version based on filemtime
        $css_path = Q_PLUGIN_DIR . 'includes/css/pwa-admin.css';
        wp_enqueue_style(
            'q-pwa-admin-styles',
            plugins_url('includes/css/pwa-admin.css', dirname(__FILE__)),
            array(),
            filemtime($css_path)
        );

        // Enqueue admin JavaScript with version based on filemtime
        $js_path = Q_PLUGIN_DIR . 'includes/js/pwa-admin.js';
        wp_enqueue_script(
            'q-pwa-admin',
            plugins_url('includes/js/pwa-admin.js', dirname(__FILE__)),
            array('jquery', 'media-editor'),
            filemtime($js_path),
            true
        );

        // Pass data to JavaScript
        wp_localize_script('q-pwa-admin', 'qPWAAdmin', [
            'manifestUrl' => Q_PWA_Manifest::get_manifest_url(),
            'siteUrl' => home_url('/'),
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('q_pwa_admin_nonce'),
            'isFirstTime' => !get_option('q_pwa_enabled', false)
        ]);
    }

    /**
     * Sanitize iOS status bar style
     */
    public static function sanitize_ios_status_bar_style($value)
    {
        $valid_styles = ['default', 'black', 'black-translucent'];
        return in_array($value, $valid_styles) ? $value : 'default';
    }

    /**
     * Get plugin version
     */
    public static function get_plugin_version()
    {
        // Try to get version from plugin data
        if (!function_exists('get_plugin_data')) {
            require_once(ABSPATH . 'wp-admin/includes/plugin.php');
        }

        $plugin_file = Q_PLUGIN_DIR . 'q-pusher.php';
        if (file_exists($plugin_file)) {
            $plugin_data = get_plugin_data($plugin_file);
            return $plugin_data['Version'] ?? '2.2.2';
        }

        return '2.2.2'; // Fallback version
    }

    /**
     * Check if an updated option is a PWA setting and flush manifest cache if needed
     */
    public static function maybe_flush_manifest_cache($option_name, $old_value, $new_value)
    {
        // Check if this is a PWA setting
        if (strpos($option_name, 'q_pwa_') === 0) {
            // Only flush if the value actually changed
            if ($old_value !== $new_value) {
                error_log('PWA Setting updated: ' . $option_name . ' - Flushing manifest cache');

                // Update a timestamp option to force manifest regeneration
                update_option('q_pwa_manifest_updated', time());

                if (class_exists('Q_PWA_Manifest')) {
                    Q_PWA_Manifest::flush_manifest_cache();
                }
                // If the caching strategy was changed, regenerate the service worker
                if ($option_name === 'q_pwa_caching_strategy') {
                    if (function_exists('q_copy_service_worker')) {
                        q_copy_service_worker();
                    }
                }
            }
        }
    }

    public static function remove_site_icon_meta()
    {
        // Only proceed if the PWA is enabled
        if (!get_option('q_pwa_enabled', false)) {
            return;
        }

        // Check if a dedicated PWA icon has been configured
        $pwa_icon = get_option('q_pwa_icon', '');
        if (empty($pwa_icon)) {
            // If there is no explicit PWA icon we keep WordPress' default site icon
            return;
        }

        // Remove the default WordPress site icon meta so the PWA icon takes precedence
        remove_action('wp_head', 'wp_site_icon', 99);
    }

    // Add a render_radio_field method if not present
    public static function render_radio_field($args)
    {
        $field = $args['field'];
        $value = get_option($field, '0');
        $options = $args['options'] ?? [1 => 'Yes', 0 => 'No'];
        $description = $args['description'] ?? '';
        foreach ($options as $opt_value => $label) {
            echo '<label style="margin-right:15px;">';
            echo '<input type="radio" name="' . esc_attr($field) . '" value="' . esc_attr($opt_value) . '"' . checked($value, $opt_value, false) . '> ' . esc_html($label);
            echo '</label>';
        }
        if ($description) {
            echo '<p class="description">' . esc_html($description) . '</p>';
        }
    }
}
