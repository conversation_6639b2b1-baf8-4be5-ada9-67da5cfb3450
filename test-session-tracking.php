<?php
/**
 * Test Session Tracking
 * 
 * This file helps debug PWA session tracking issues.
 * Place this file in your WordPress root directory and access it via browser.
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

// Force analytics to be enabled for testing
update_option('q_pwa_analytics_enabled', true);

?>
<!DOCTYPE html>
<html>
<head>
    <title>PWA Session Tracking Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-box { background: #f9f9f9; padding: 15px; border: 1px solid #ddd; margin: 10px 0; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { padding: 10px 15px; margin: 5px; }
        #log { background: #000; color: #0f0; padding: 10px; height: 200px; overflow-y: scroll; font-family: monospace; }
    </style>
</head>
<body>
    <h1>PWA Session Tracking Test</h1>
    
    <div class="debug-box">
        <h3>Current Status</h3>
        <p><strong>Analytics Enabled:</strong> <?php echo get_option('q_pwa_analytics_enabled', false) ? 'Yes' : 'No'; ?></p>
        <p><strong>Current User ID:</strong> <?php echo get_current_user_id() ?: 'Not logged in'; ?></p>
        <p><strong>WordPress AJAX URL:</strong> <?php echo admin_url('admin-ajax.php'); ?></p>
        <p><strong>PWA Nonce:</strong> <?php echo wp_create_nonce('q_pwa_nonce'); ?></p>
    </div>

    <div class="debug-box">
        <h3>Session Debug Data</h3>
        <?php echo do_shortcode('[pwa_debug_sessions limit="5"]'); ?>
    </div>

    <div class="debug-box">
        <h3>Current Session Duration</h3>
        <p><?php echo do_shortcode('[pwa_user_analytics event="avg_session_duration"]'); ?></p>
    </div>

    <div class="debug-box">
        <h3>Manual Session Testing</h3>
        <button onclick="startTestSession()">Start Test Session</button>
        <button onclick="endTestSession()">End Test Session</button>
        <button onclick="sendTestEvent()">Send Test Event</button>
        <button onclick="clearLog()">Clear Log</button>
        <div id="log"></div>
    </div>

    <script>
        // Add WordPress settings that the analytics script expects
        window.qPWASettings = {
            enabled: true,
            analyticsEnabled: true,
            ajaxUrl: '<?php echo admin_url('admin-ajax.php'); ?>',
            nonce: '<?php echo wp_create_nonce('q_pwa_nonce'); ?>',
            currentUserId: <?php echo get_current_user_id() ?: 0; ?>,
            appVersion: '1.0.0'
        };

        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // Copy essential functions from pwa-analytics-tracking.js
        function getUserId() {
            if (qPWASettings && qPWASettings.currentUserId && qPWASettings.currentUserId > 0) {
                return qPWASettings.currentUserId;
            }
            let userId = localStorage.getItem('q_pwa_user_id');
            if (!userId) {
                userId = 'user_' + Math.random().toString(36).substr(2, 9);
                localStorage.setItem('q_pwa_user_id', userId);
            }
            return userId;
        }

        function getDeviceType() {
            const ua = navigator.userAgent;
            if (/(tablet|ipad|playbook|silk)|(android(?!.*mobi))/i.test(ua)) {
                return 'tablet';
            }
            if (/Mobile|Android|iP(hone|od)|IEMobile|BlackBerry|Kindle|Silk-Accelerated|(hpw|web)OS|Opera M(obi|ini)/.test(ua)) {
                return 'mobile';
            }
            return 'desktop';
        }

        function getOS() {
            const ua = navigator.userAgent;
            if (ua.indexOf('Windows') !== -1) return 'Windows';
            if (ua.indexOf('Mac') !== -1) return 'macOS';
            if (ua.indexOf('Linux') !== -1) return 'Linux';
            if (ua.indexOf('Android') !== -1) return 'Android';
            if (ua.indexOf('iOS') !== -1) return 'iOS';
            return 'Unknown';
        }

        function getBrowser() {
            const ua = navigator.userAgent;
            if (ua.indexOf('Chrome') !== -1) return 'Chrome';
            if (ua.indexOf('Firefox') !== -1) return 'Firefox';
            if (ua.indexOf('Safari') !== -1) return 'Safari';
            if (ua.indexOf('Edge') !== -1) return 'Edge';
            return 'Unknown';
        }

        function trackEvent(eventName, data = {}) {
            log(`Tracking event: ${eventName}`);
            
            const eventData = {
                event_name: eventName,
                user_id: getUserId(),
                device_type: getDeviceType(),
                os: getOS(),
                browser: getBrowser(),
                app_version: qPWASettings.appVersion,
                timestamp: new Date().toISOString(),
                ...data
            };

            // For session events, add session ID
            if (eventName === 'session_start' || eventName === 'session_end') {
                eventData.session_id = localStorage.getItem('q_pwa_session_id');
            }

            log(`Event data: ${JSON.stringify(eventData, null, 2)}`);

            fetch(qPWASettings.ajaxUrl, {
                method: 'POST',
                credentials: 'same-origin',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: new URLSearchParams({
                    action: 'q_pwa_track_event',
                    nonce: qPWASettings.nonce,
                    event_data: JSON.stringify(eventData)
                })
            })
            .then(response => response.json())
            .then(data => {
                log(`Server response: ${JSON.stringify(data)}`);
            })
            .catch(error => {
                log(`Error: ${error.message}`);
            });
        }

        function startTestSession() {
            const sessionId = 'sess_' + Math.random().toString(36).substr(2, 9);
            localStorage.setItem('q_pwa_session_id', sessionId);
            localStorage.setItem('q_pwa_session_start', new Date().toISOString());
            
            log(`Starting session: ${sessionId}`);
            
            trackEvent('session_start', {
                session_id: sessionId
            });
        }

        function endTestSession() {
            const sessionId = localStorage.getItem('q_pwa_session_id');
            const sessionStart = localStorage.getItem('q_pwa_session_start');
            
            if (sessionId && sessionStart) {
                const startTime = new Date(sessionStart);
                const endTime = new Date();
                const duration = endTime - startTime;
                
                log(`Ending session: ${sessionId}, Duration: ${duration}ms`);
                
                trackEvent('session_end', {
                    session_id: sessionId,
                    session_duration: duration
                });
                
                localStorage.removeItem('q_pwa_session_id');
                localStorage.removeItem('q_pwa_session_start');
            } else {
                log('No active session to end');
            }
        }

        function sendTestEvent() {
            trackEvent('test_event', {
                test_data: 'This is a test event'
            });
        }

        // Initialize
        log('Test page loaded');
        log(`User ID: ${getUserId()}`);
        log(`Device: ${getDeviceType()}`);
        log(`OS: ${getOS()}`);
        log(`Browser: ${getBrowser()}`);
    </script>
</body>
</html>
