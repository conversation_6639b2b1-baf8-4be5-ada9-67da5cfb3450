<?php
if (!defined('ABSPATH')) {
    die('You are not allowed to call this page directly.');
}

// Only declare functions if they don't already exist
if (!function_exists('q_track_notification')) {
    function q_track_notification($notification_id, $type, $data = [], $form_id = null)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'q_notification_analytics';

        // Ensure the table exists
        q_create_analytics_table();

        $result = $wpdb->insert(
            $table_name,
            array(
                'notification_id' => $notification_id,
                'type' => $type,
                'data' => json_encode($data),
                'form_id' => $form_id,
                'timestamp' => current_time('mysql')
            ),
            array('%s', '%s', '%s', '%d', '%s')
        );

        if ($result === false) {
            error_log('Failed to track notification: ' . $wpdb->last_error);
        }

        return $result;
    }
}

if (!function_exists('q_create_analytics_table')) {
    function q_create_analytics_table()
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'q_notification_analytics';

        if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

            $charset_collate = $wpdb->get_charset_collate();
            $sql = "CREATE TABLE IF NOT EXISTS $table_name (
                id bigint(20) NOT NULL AUTO_INCREMENT,
                notification_id varchar(36) NOT NULL,
                type varchar(50) NOT NULL,
                data longtext,
                form_id bigint(20),
                timestamp datetime DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY  (id),
                KEY notification_id (notification_id),
                KEY type (type),
                KEY form_id (form_id)
            ) $charset_collate;";

            dbDelta($sql);
        }
        // Also create the PWA analytics table
        q_create_pwa_analytics_table();
    }
}

if (!function_exists('q_create_pwa_analytics_table')) {
    function q_create_pwa_analytics_table()
    {
        if (class_exists('Q_PWA_Analytics')) {
            // Delegate to the canonical table-creation helper to prevent duplicate SQL definitions.
            Q_PWA_Analytics::create_tables();
        }
    }
}

if (!function_exists('q_get_total_notifications')) {
    function q_get_total_notifications($form_id = null)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'q_notification_analytics';

        $query = "SELECT COUNT(DISTINCT notification_id) FROM $table_name WHERE type = 'sent'";
        $params = [];

        if ($form_id !== null) {
            $query .= " AND form_id = %d";
            $params[] = $form_id;
        }

        if (!empty($params)) {
            $query = $wpdb->prepare($query, ...$params);
        }

        $count = $wpdb->get_var($query);
        return intval($count ?? 0);
    }
}

if (!function_exists('q_get_engagement_rate')) {
    function q_get_engagement_rate($form_id = null)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'q_notification_analytics';

        // Base queries
        $sent_query = "SELECT COUNT(DISTINCT notification_id) FROM $table_name WHERE type = 'sent'";
        $click_query = "SELECT COUNT(DISTINCT notification_id) FROM $table_name WHERE type = 'click'";

        $params = [];

        // Add form_id condition if specified
        if ($form_id !== null) {
            $sent_query .= " AND form_id = %d";
            $click_query .= " AND form_id = %d";
            $params[] = $form_id;
            $params[] = $form_id;
        }

        // Prepare queries if we have parameters
        if (!empty($params)) {
            $sent_query = $wpdb->prepare($sent_query, ...$params);
            $click_query = $wpdb->prepare($click_query, ...$params);
        }

        $total_sent = intval($wpdb->get_var($sent_query) ?? 0);
        $total_clicks = intval($wpdb->get_var($click_query) ?? 0);

        if ($total_sent === 0) {
            return 0;
        }

        return round(($total_clicks / $total_sent) * 100, 1);
    }
}

if (!function_exists('q_get_form_analytics')) {
    function q_get_form_analytics()
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'q_notification_analytics';

        // First check if the form_id column exists
        $column_exists = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_SCHEMA = %s
            AND TABLE_NAME = %s
            AND COLUMN_NAME = 'form_id'",
            DB_NAME,
            $table_name
        ));

        if (empty($column_exists)) {
            error_log('Form analytics table needs updating. Please deactivate and reactivate the plugin.');
            return array();
        }

        // Get analytics for all forms with notifications
        $results = $wpdb->get_results("
            SELECT
                form_id,
                COUNT(DISTINCT CASE WHEN type = 'sent' THEN notification_id END) as total_notifications,
                COUNT(DISTINCT CASE WHEN type = 'click' THEN notification_id END) as total_clicks
            FROM $table_name
            WHERE form_id IS NOT NULL
            GROUP BY form_id
            ORDER BY form_id ASC
        ");

        $analytics = array();
        foreach ($results as $result) {
            $form_id = $result->form_id;
            $engagement_rate = $result->total_notifications > 0
                ? round(($result->total_clicks / $result->total_notifications) * 100, 1)
                : 0;

            $analytics[$form_id] = array(
                'total_notifications' => $result->total_notifications,
                'engagement_rate' => $engagement_rate
            );
        }

        return $analytics;
    }
}

if (!function_exists('q_track_notification_click')) {
    function q_track_notification_click()
    {
        if (!isset($_POST['notification_id'])) {
            wp_send_json_error('Missing notification ID');
            return;
        }

        $notification_id = sanitize_text_field($_POST['notification_id']);
        $form_id = isset($_POST['form_id']) ? intval($_POST['form_id']) : null;

        q_track_notification($notification_id, 'click', [
            'timestamp' => current_time('mysql'),
            'user_agent' => $_SERVER['HTTP_USER_AGENT']
        ], $form_id);

        wp_send_json_success();
    }
}

// Add AJAX handler for tracking notification clicks
add_action('wp_ajax_q_track_notification_click', 'q_track_notification_click');
add_action('wp_ajax_nopriv_q_track_notification_click', 'q_track_notification_click');

if (!function_exists('q_track_pwa_event')) {
    function q_track_pwa_event()
    {
        if (!isset($_POST['event']) || !isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'q_pwa_event_nonce')) {
            wp_send_json_error('Invalid nonce or missing event data.');
            return;
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'q_pwa_analytics';

        $event_name = sanitize_text_field($_POST['event']);
        $event_data = isset($_POST['data']) ? json_decode(stripslashes($_POST['data']), true) : [];
        $timestamp = isset($_POST['timestamp']) ? sanitize_text_field($_POST['timestamp']) : current_time('mysql');

        $user_id = get_current_user_id();
        $session_id = session_id(); // Get PHP session ID
        if (empty($session_id)) {
            // Fallback for session ID if not started or available
            $session_id = isset($_COOKIE['PHPSESSID']) ? sanitize_text_field($_COOKIE['PHPSESSID']) : uniqid('pwa_session_');
        }

        $device_info = $_SERVER['HTTP_USER_AGENT'];
        $page_url = isset($_SERVER['HTTP_REFERER']) ? esc_url_raw($_SERVER['HTTP_REFERER']) : '';

        $result = $wpdb->insert(
            $table_name,
            array(
                'event_name' => $event_name,
                'user_id' => $user_id,
                'session_id' => $session_id,
                'device_info' => $device_info,
                'page_url' => $page_url,
                'event_data' => json_encode($event_data),
                'timestamp' => $timestamp
            ),
            array('%s', '%d', '%s', '%s', '%s', '%s', '%s')
        );

        if ($result === false) {
            error_log('Failed to track PWA event: ' . $wpdb->last_error);
            wp_send_json_error('Failed to track event.');
        } else {
            wp_send_json_success('Event tracked successfully.');
        }
    }
}

add_action('wp_ajax_q_track_pwa_event', 'q_track_pwa_event');
add_action('wp_ajax_nopriv_q_track_pwa_event', 'q_track_pwa_event');

// Create analytics table on plugin activation
register_activation_hook(Q_PLUGIN_DIR . 'q-pusher.php', 'q_create_analytics_table');

/**
 * Find unclicked notifications that need to be retriggered
 *
 * @return array Array of notifications to retrigger
 */
if (!function_exists('q_find_unclicked_notifications')) {
    function q_find_unclicked_notifications()
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'q_notification_analytics';

        // Get sent notifications with retrigger enabled that haven't been clicked
        $query = "
            SELECT s.notification_id, s.data, s.form_id, s.timestamp
            FROM $table_name s
            LEFT JOIN (
                SELECT notification_id
                FROM $table_name
                WHERE type = 'click'
            ) c ON s.notification_id = c.notification_id
            WHERE s.type = 'sent'
            AND c.notification_id IS NULL
            AND (
                s.data LIKE '%\"enable_retrigger\":true%'
                OR s.data LIKE '%\"enable_retrigger\":\"true\"%'
            )
            ORDER BY s.timestamp ASC
        ";

        // error_log('Q-Pusher Debug - Running query: ' . $query);

        $results = $wpdb->get_results($query);
        $notifications_to_retrigger = [];

        foreach ($results as $result) {
            $data = json_decode($result->data, true);
            // error_log('Q-Pusher Debug - Examining notification: ' . $result->notification_id);
            // error_log('Q-Pusher Debug - Notification data: ' . print_r($data, true));

            // Skip if required data is missing
            if (!isset($data['enable_retrigger'])) {
                // error_log('Q-Pusher Debug - Missing enable_retrigger setting');
                continue;
            }

            // Handle different data formats (string vs boolean)
            $enable_retrigger = is_string($data['enable_retrigger'])
                ? ($data['enable_retrigger'] === 'true')
                : (bool) $data['enable_retrigger'];

            if (!$enable_retrigger) {
                // error_log('Q-Pusher Debug - Retriggering not enabled for this notification');
                continue;
            }

            // Set default values if missing
            if (!isset($data['retrigger_delay'])) {
                $data['retrigger_delay'] = 1; // Default to 1 hour
                // error_log('Q-Pusher Debug - Using default retrigger delay: 1 hour');
            }

            if (!isset($data['retrigger_max_attempts'])) {
                $data['retrigger_max_attempts'] = 3; // Default to 3 attempts
                // error_log('Q-Pusher Debug - Using default max attempts: 3');
            }

            if (!isset($data['retrigger_attempt'])) {
                $data['retrigger_attempt'] = 0; // Default to first attempt
                // error_log('Q-Pusher Debug - Using default current attempt: 0');
            }

            // Check if we've reached max attempts
            $current_attempt = intval($data['retrigger_attempt']);
            $max_attempts = intval($data['retrigger_max_attempts']);

            if ($current_attempt >= $max_attempts) {
                // error_log('Q-Pusher Debug - Max attempts reached: ' . $current_attempt . '/' . $max_attempts);
                continue;
            }

            // Find the timestamp of the last retrigger attempt for this notification
            $last_retrigger_time = $sent_time = strtotime($result->timestamp);

            if ($current_attempt > 0) {
                // If this notification has been retriggered before, find the most recent retrigger timestamp
                $last_retrigger_query = $wpdb->prepare(
                    "SELECT timestamp FROM $table_name
                    WHERE type = 'sent'
                    AND data LIKE %s
                    AND data LIKE %s
                    ORDER BY timestamp DESC
                    LIMIT 1",
                    '%"original_notification_id":"' . $result->notification_id . '"%',
                    '%"is_retrigger":true%'
                );

                $last_retrigger = $wpdb->get_var($last_retrigger_query);

                if ($last_retrigger) {
                    $last_retrigger_time = strtotime($last_retrigger);
                    // error_log('Q-Pusher Debug - Found previous retrigger at: ' . date('Y-m-d H:i:s', $last_retrigger_time));
                }
            }

            // Check if enough time has passed since the last retrigger (or original notification if no retriggers)
            $delay_hours = intval($data['retrigger_delay']);
            $current_time = time();
            $delay_seconds = $delay_hours * 3600;

            $time_diff = $current_time - $last_retrigger_time;
            $hours_diff = $time_diff / 3600;

            // error_log(sprintf(
            //     'Q-Pusher Debug - Time check: Last notification/retrigger=%s, Now=%s, Diff=%.2f hours, Required=%d hours',
            //     date('Y-m-d H:i:s', $last_retrigger_time),
            //     date('Y-m-d H:i:s', $current_time),
            //     $hours_diff,
            //     $delay_hours
            // ));

            if ($time_diff >= $delay_seconds) {
                // error_log('Q-Pusher Debug - Time threshold met, notification will be retriggered');

                // This notification should be retriggered
                $notifications_to_retrigger[] = [
                    'notification_id' => $result->notification_id,
                    'form_id' => $result->form_id,
                    'data' => $data,
                    'current_attempt' => $current_attempt,
                    'action_id' => isset($data['action_id']) ? intval($data['action_id']) : 0
                ];
            } else {
                // error_log('Q-Pusher Debug - Time threshold not met yet, notification will be checked again later');
            }
        }

        return $notifications_to_retrigger;
    }
}

/**
 * Retrigger notifications that haven't been clicked
 *
 * @return int Number of notifications retriggered
 */
if (!function_exists('q_retrigger_notifications')) {
    function q_retrigger_notifications()
    {
        // Find notifications that need to be retriggered
        $notifications = q_find_unclicked_notifications();

        if (empty($notifications)) {
            return 0;
        }

        $retriggered_count = 0;

        foreach ($notifications as $notification) {
            $data = $notification['data'];
            $current_attempt = $notification['current_attempt'];

            // Get the user token from the data
            if (!isset($data['token']) || empty($data['token'])) {
                continue;
            }

            // Get the full token from the database using the partial token
            $token_partial = $data['token'];
            $user_id = q_get_user_id_by_token_partial($token_partial);

            if (!$user_id) {
                continue;
            }

            $token = get_user_meta($user_id, 'q_push_token', true);

            if (empty($token)) {
                continue;
            }

            // Prepare notification data for retriggering
            $title = isset($data['title']) ? $data['title'] : 'Notification Reminder';
            $message = isset($data['message']) ? $data['message'] : 'You have an unread notification.';
            $image = isset($data['image']) ? $data['image'] : '';

            // Log the retrigger attempt
            // error_log(sprintf(
            //     'Q-Pusher Debug - Retriggering notification: ID=%s, Attempt=%d/%d',
            //     $notification['notification_id'],
            //     $current_attempt + 1,
            //     isset($data['retrigger_max_attempts']) ? intval($data['retrigger_max_attempts']) : 3
            // ));

            // Increment the attempt counter and add retrigger metadata
            $additional_data = [
                'form_id' => $notification['form_id'],
                'action_id' => $notification['action_id'],
                'enable_retrigger' => true,
                'retrigger_delay' => isset($data['retrigger_delay']) ? intval($data['retrigger_delay']) : 24,
                'retrigger_max_attempts' => isset($data['retrigger_max_attempts']) ? intval($data['retrigger_max_attempts']) : 3,
                'retrigger_attempt' => $current_attempt + 1,
                'is_retrigger' => true,
                'original_notification_id' => $notification['notification_id'],
                'last_retrigger_time' => time() // Store the timestamp of this retrigger
            ];

            // Send the notification
            $result = q_send_push_notification(
                $token,
                $title . ' (Reminder)',
                $message,
                $image,
                false,
                'reminder',
                $additional_data
            );

            if ($result) {
                $retriggered_count++;
                // error_log('Q-Pusher Debug - Successfully sent retrigger notification');
            } else {
                // error_log('Q-Pusher Debug - Failed to send retrigger notification');
            }
        }

        return $retriggered_count;
    }
}

/**
 * Get user ID by partial token
 *
 * @param string $token_partial Partial token (usually first 10 chars followed by '...')
 * @return int|false User ID or false if not found
 */
if (!function_exists('q_get_user_id_by_token_partial')) {
    function q_get_user_id_by_token_partial($token_partial)
    {
        global $wpdb;

        // Extract the actual partial token (remove the '...' if present)
        $clean_partial = str_replace('...', '', $token_partial);

        // Find users with matching token
        $query = $wpdb->prepare(
            "SELECT user_id FROM {$wpdb->usermeta}
            WHERE meta_key = 'q_push_token'
            AND meta_value LIKE %s
            LIMIT 1",
            $clean_partial . '%'
        );

        return $wpdb->get_var($query);
    }
}

// Schedule the retrigger check
if (!function_exists('q_schedule_notification_retrigger')) {
    function q_schedule_notification_retrigger()
    {
        if (!wp_next_scheduled('q_check_notification_retriggers')) {
            wp_schedule_event(time(), 'hourly', 'q_check_notification_retriggers');
            // error_log('Q-Pusher: Scheduled notification retrigger check');
        }
    }
}

// Debug function to check scheduled events
if (!function_exists('q_debug_scheduled_events')) {
    function q_debug_scheduled_events()
    {
        $cron = _get_cron_array();
        $scheduled = wp_next_scheduled('q_check_notification_retriggers');

        // error_log('Q-Pusher Debug - Next scheduled retrigger check: ' .
        //     ($scheduled ? date('Y-m-d H:i:s', $scheduled) : 'Not scheduled'));

        // Check if we have any pending notifications to retrigger
        $notifications = q_find_unclicked_notifications();
        // error_log('Q-Pusher Debug - Found ' . count($notifications) . ' notifications to retrigger');

        foreach ($notifications as $index => $notification) {
            // error_log('Q-Pusher Debug - Notification #' . ($index + 1) . ': ' .
            //     'ID=' . $notification['notification_id'] . ', ' .
            //     'Attempt=' . $notification['current_attempt'] . ', ' .
            //     'Form ID=' . $notification['form_id']);
        }

        return $scheduled;
    }
}

// Hook for the scheduled event
add_action('q_check_notification_retriggers', 'q_retrigger_notifications');

// Add an admin AJAX endpoint for manually triggering notification checks
if (!function_exists('q_manual_check_notifications')) {
    function q_manual_check_notifications()
    {
        // Verify admin permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Permission denied');
            return;
        }

        // Run the debug function first
        q_debug_scheduled_events();

        // Run the retrigger function
        $count = q_retrigger_notifications();

        wp_send_json_success([
            'message' => sprintf('Manually checked notifications. Retriggered %d notifications.', $count),
            'count' => $count
        ]);
    }
}

// Register the AJAX endpoint
add_action('wp_ajax_q_manual_check_notifications', 'q_manual_check_notifications');

// Add an admin AJAX endpoint for checking notification retrigger history
if (!function_exists('q_check_notification_history')) {
    function q_check_notification_history()
    {
        // Verify admin permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Permission denied');
            return;
        }

        if (!isset($_POST['notification_id'])) {
            wp_send_json_error('Missing notification ID');
            return;
        }

        $notification_id = sanitize_text_field($_POST['notification_id']);
        $history = q_get_notification_retrigger_history($notification_id);

        wp_send_json_success($history);
    }
}

// Register the history check AJAX endpoint
add_action('wp_ajax_q_check_notification_history', 'q_check_notification_history');

// Schedule the event on plugin activation
register_activation_hook(Q_PLUGIN_DIR . 'q-pusher.php', 'q_schedule_notification_retrigger');

// Unschedule the event on plugin deactivation
register_deactivation_hook(Q_PLUGIN_DIR . 'q-pusher.php', function () {
    wp_clear_scheduled_hook('q_check_notification_retriggers');
});

/**
 * Debug function to get retrigger history for a notification
 *
 * @param string $notification_id The original notification ID
 * @return array Retrigger history
 */
if (!function_exists('q_get_notification_retrigger_history')) {
    function q_get_notification_retrigger_history($notification_id)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'q_notification_analytics';

        // Get the original notification
        $original_query = $wpdb->prepare(
            "SELECT * FROM $table_name
            WHERE notification_id = %s
            AND type = 'sent'",
            $notification_id
        );

        $original = $wpdb->get_row($original_query);

        if (!$original) {
            return ['error' => 'Original notification not found'];
        }

        // Get all retriggers for this notification
        $retriggers_query = $wpdb->prepare(
            "SELECT * FROM $table_name
            WHERE type = 'sent'
            AND data LIKE %s
            ORDER BY timestamp ASC",
            '%"original_notification_id":"' . $notification_id . '"%'
        );

        $retriggers = $wpdb->get_results($retriggers_query);

        // Check if notification was clicked
        $click_query = $wpdb->prepare(
            "SELECT * FROM $table_name
            WHERE notification_id = %s
            AND type = 'click'",
            $notification_id
        );

        $click = $wpdb->get_row($click_query);

        // Format the results
        $history = [
            'original' => [
                'notification_id' => $original->notification_id,
                'timestamp' => $original->timestamp,
                'data' => json_decode($original->data, true)
            ],
            'retriggers' => [],
            'clicked' => !empty($click),
            'click_time' => !empty($click) ? $click->timestamp : null
        ];

        foreach ($retriggers as $retrigger) {
            $data = json_decode($retrigger->data, true);
            $history['retriggers'][] = [
                'notification_id' => $retrigger->notification_id,
                'timestamp' => $retrigger->timestamp,
                'attempt' => isset($data['retrigger_attempt']) ? intval($data['retrigger_attempt']) : 0
            ];
        }

        return $history;
    }
}


if (!function_exists('q_get_pwa_analytics')) {
    function q_get_pwa_analytics($period = 'all', $limit = 10)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'q_pwa_analytics';

        // Check if the table exists to prevent errors
        if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
            return [];
        }

        // Add time period filter
        $where_clause = '';
        if ($period !== 'all') {
            $time_periods = [
                'today' => 'DATE(timestamp) = CURDATE()',
                'yesterday' => 'DATE(timestamp) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)',
                'week' => 'timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY)',
                'month' => 'timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)',
            ];

            if (isset($time_periods[$period])) {
                $where_clause = 'WHERE ' . $time_periods[$period];
            }
        }

        // Get event counts
        $results = $wpdb->get_results(
            "
            SELECT event_name, COUNT(id) as count
            FROM {$table_name}
            {$where_clause}
            GROUP BY event_name
            ORDER BY count DESC
            " . ($limit > 0 ? "LIMIT {$limit}" : ""),
            ARRAY_A
        );

        if (empty($results)) {
            return [];
        }

        return $results;
    }
}

if (!function_exists('q_pwa_analytics_shortcode')) {
    /**
     * Shortcode to display PWA analytics counts.
     *
     * @param array $atts Shortcode attributes.
     * @return string The event count or an empty string.
     */
    function q_pwa_analytics_shortcode($atts)
    {
        $atts = shortcode_atts(
            array(
                'event_type' => '',
                'period' => 'all', // all, today, yesterday, week, month
                'format' => 'number', // number, percentage, chart
            ),
            $atts,
            'pwa_analytics'
        );

        $event_type = sanitize_text_field($atts['event_type']);
        $period = sanitize_text_field($atts['period']);
        $format = sanitize_text_field($atts['format']);

        if (empty($event_type)) {
            return ''; // or return 'Error: event_type is required.';
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'q_pwa_analytics';

        // Check if the table exists to prevent errors
        if ($wpdb->get_var($wpdb->prepare("SHOW TABLES LIKE %s", $table_name)) != $table_name) {
            return '0';
        }

        // Build the query based on period
        $where_clause = "WHERE event_name = %s";
        $query_params = [$event_type];

        if ($period !== 'all') {
            $time_periods = [
                'today' => 'AND DATE(timestamp) = CURDATE()',
                'yesterday' => 'AND DATE(timestamp) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)',
                'week' => 'AND timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY)',
                'month' => 'AND timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)',
            ];

            if (isset($time_periods[$period])) {
                $where_clause .= " " . $time_periods[$period];
            }
        }

        $count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(id) FROM {$table_name} {$where_clause}",
            $query_params
        ));

        // Format the output
        if ($format === 'percentage') {
            // Get total events for the period
            $total_query = "SELECT COUNT(id) FROM {$table_name}";
            if ($period !== 'all' && isset($time_periods[$period])) {
                $total_query .= " WHERE " . substr($time_periods[$period], 4); // Remove the "AND " prefix
            }
            $total = $wpdb->get_var($total_query);

            if ($total > 0) {
                $percentage = round(($count / $total) * 100, 1);
                return $percentage . '%';
            }
            return '0%';
        }

        return number_format_i18n($count ?? 0);
    }
}

add_shortcode('pwa_analytics', 'q_pwa_analytics_shortcode');

/**
 * AJAX handler for tracking PWA events
 */
if (!function_exists('q_pwa_track_event')) {
    function q_pwa_track_event()
    {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'q_pwa_nonce')) {
            wp_send_json_error('Invalid nonce');
            return;
        }

        // Check if analytics is enabled
        if (!get_option('q_pwa_analytics_enabled', true)) {
            wp_send_json_success('Analytics disabled');
            return;
        }

        // Get event data
        $event_data = isset($_POST['event_data']) ? json_decode(stripslashes($_POST['event_data']), true) : [];

        if (empty($event_data) || !isset($event_data['event_name'])) {
            wp_send_json_error('Invalid event data');
            return;
        }

        // Sanitize data
        $event_name = sanitize_text_field($event_data['event_name']);
        $user_id = isset($event_data['user_id']) ? sanitize_text_field($event_data['user_id']) : '';
        $device_type = isset($event_data['device_type']) ? sanitize_text_field($event_data['device_type']) : '';
        $os = isset($event_data['os']) ? sanitize_text_field($event_data['os']) : '';
        $browser = isset($event_data['browser']) ? sanitize_text_field($event_data['browser']) : '';
        $app_version = isset($event_data['app_version']) ? sanitize_text_field($event_data['app_version']) : '';

        // Remove sensitive fields and sanitize remaining data
        unset($event_data['event_name']);
        unset($event_data['user_id']);
        unset($event_data['device_type']);
        unset($event_data['os']);
        unset($event_data['browser']);
        unset($event_data['app_version']);
        unset($event_data['timestamp']); // Use server timestamp instead

        // Sanitize any remaining data
        $data = !empty($event_data) ? json_encode($event_data) : null;

        // Store in database
        global $wpdb;
        $table_name = $wpdb->prefix . 'q_pwa_analytics';

        /* ------------------------------------------------------------------
         * Schema safeguard: Older installations may lack one or more columns
         * that this handler now relies on (e.g., device_type, os, browser, app_version, or event_data).
         * To avoid "unknown column" SQL errors, we verify the schema at
         * runtime and trigger the dbDelta-powered upgrade if needed.
         * ------------------------------------------------------------------ */
        $existing_columns = $wpdb->get_col("DESCRIBE {$table_name}");
        $required_columns = ['device_type', 'event_data', 'os', 'browser', 'app_version'];
        $missing_columns = array_diff($required_columns, $existing_columns);
        if (!empty($missing_columns)) {
            if (class_exists('Q_PWA_Analytics')) {
                Q_PWA_Analytics::create_tables(); // dbDelta will add any missing columns without data loss
            }
        }

        $result = $wpdb->insert(
            $table_name,
            [
                'event_name' => $event_name,
                'user_id' => $user_id,
                'device_type' => $device_type,
                'os' => $os,
                'browser' => $browser,
                'app_version' => $app_version,
                // Store any additional payload in the canonical "event_data" column so that
                // the schema matches Q_PWA_Analytics::create_tables(). Using a consistent
                // column name prevents "unknown column" SQL errors after upgrades.
                'event_data' => $data
            ],
            [
                '%s',
                '%s',
                '%s',
                '%s',
                '%s',
                '%s',
                '%s'
            ]
        );

        if ($result) {
            wp_send_json_success('Event tracked');
        } else {
            wp_send_json_error('Failed to track event');
        }
    }
}

// Register AJAX handlers
add_action('wp_ajax_q_pwa_track_event', 'q_pwa_track_event');
add_action('wp_ajax_nopriv_q_pwa_track_event', 'q_pwa_track_event');

/**
 * Get PWA analytics data for time-based charts
 * 
 * @param string $event_type Event type to filter
 * @param string $period Period to analyze (day, week, month)
 * @param int $points Number of data points to return
 * @return array Analytics data for charting
 */
if (!function_exists('q_get_pwa_analytics_chart_data')) {
    function q_get_pwa_analytics_chart_data($event_type = '', $period = 'week', $points = 7)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'q_pwa_analytics';

        // Check if the table exists to prevent errors
        if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
            return [];
        }

        $where_clause = '';
        $group_by = '';
        $date_format = '';

        switch ($period) {
            case 'day':
                $date_format = '%H:00'; // Hours in a day
                $group_by = 'HOUR(timestamp)';
                $where_clause = 'WHERE DATE(timestamp) = CURDATE()';
                $points = min($points, 24); // Max 24 hours in a day
                break;

            case 'week':
                $date_format = '%a'; // Day name
                $group_by = 'DAYOFWEEK(timestamp)';
                $where_clause = 'WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
                $points = min($points, 7); // Max 7 days in a week
                break;

            case 'month':
                $date_format = '%d'; // Day of month
                $group_by = 'DAY(timestamp)';
                $where_clause = 'WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)';
                $points = min($points, 31); // Max 31 days in a month
                break;

            default:
                return [];
        }

        // Add event type filter if provided
        if (!empty($event_type)) {
            $where_clause .= $where_clause ? ' AND ' : 'WHERE ';
            $where_clause .= $wpdb->prepare('event_name = %s', $event_type);
        }

        // Get the data
        $results = $wpdb->get_results($wpdb->prepare("
            SELECT 
                DATE_FORMAT(timestamp, %s) as label,
                {$group_by} as period_value,
                COUNT(id) as count
            FROM {$table_name}
            {$where_clause}
            GROUP BY period_value
            ORDER BY period_value ASC
            LIMIT %d
        ", $date_format, $points), ARRAY_A);

        return $results;
    }
}
